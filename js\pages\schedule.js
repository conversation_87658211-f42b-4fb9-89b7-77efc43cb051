/**
 * SCHEDULE PAGE FUNCTIONALITY
 * 
 * This file contains all the JavaScript functionality for the schedule page,
 * including tournament selection, filtering, and timeline display.
 */

class SchedulePage {
    constructor() {
        this.currentTournament = null;
        this.currentFilter = 'all';
        this.currentDateFilter = null;
        this.scheduleContainer = null;
        this.tournamentTabs = null;
        this.filterButtons = null;
        this.dateButtons = null;
        
        this.init();
    }
    
    /**
     * Initialize the schedule page
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    /**
     * Set up the schedule page elements and event listeners
     */
    setup() {
        // Get DOM elements
        this.scheduleContainer = document.getElementById('schedule-container');
        this.tournamentTabs = document.getElementById('tournament-tabs');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.dateButtons = document.querySelectorAll('.date-btn');
        
        if (!this.scheduleContainer || !this.tournamentTabs) {
            console.error('Required containers not found');
            return;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Load tournament tabs
        this.loadTournamentTabs();
        
        // Set default tournament (first ongoing or upcoming tournament)
        this.setDefaultTournament();
        
        // Set up navigation
        this.setupNavigation();
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Filter button event listeners
        this.filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const filter = e.target.getAttribute('data-filter');
                this.setFilter(filter);
            });
        });
        
        // Date button event listeners
        this.dateButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const dateFilter = e.target.getAttribute('data-date');
                this.setDateFilter(dateFilter);
            });
        });
        
        // Mobile navigation toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }
    }
    
    /**
     * Set up navigation highlighting
     */
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
    
    /**
     * Load tournament tabs
     */
    loadTournamentTabs() {
        const tournaments = [...TOURNAMENTS].sort((a, b) => {
            // Sort by status priority (ongoing > upcoming > completed)
            const statusPriority = { ongoing: 3, upcoming: 2, completed: 1, cancelled: 0 };
            return (statusPriority[b.status] || 0) - (statusPriority[a.status] || 0);
        });
        
        this.tournamentTabs.innerHTML = tournaments.map(tournament => `
            <button class="tournament-tab" data-tournament-id="${tournament.id}">
                ${tournament.shortName || tournament.name}
                <span class="tournament-status">${tournament.status}</span>
            </button>
        `).join('');
        
        // Add event listeners to tournament tabs
        const tabs = this.tournamentTabs.querySelectorAll('.tournament-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tournamentId = e.target.getAttribute('data-tournament-id');
                this.setTournament(tournamentId);
            });
        });
    }
    
    /**
     * Set default tournament
     */
    setDefaultTournament() {
        // Find first ongoing tournament, then upcoming, then any
        let defaultTournament = TOURNAMENTS.find(t => t.status === 'ongoing') ||
                               TOURNAMENTS.find(t => t.status === 'upcoming') ||
                               TOURNAMENTS[0];
        
        if (defaultTournament) {
            this.setTournament(defaultTournament.id);
        }
    }
    
    /**
     * Set the current tournament
     * @param {string} tournamentId - Tournament ID
     */
    setTournament(tournamentId) {
        this.currentTournament = getTournamentById(tournamentId);
        
        if (!this.currentTournament) return;
        
        // Update tournament tab states
        const tabs = this.tournamentTabs.querySelectorAll('.tournament-tab');
        tabs.forEach(tab => {
            if (tab.getAttribute('data-tournament-id') === tournamentId) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
        
        // Load schedule for this tournament
        this.loadSchedule();
    }
    
    /**
     * Set the current filter
     * @param {string} filter - Filter type
     */
    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update filter button states
        this.filterButtons.forEach(button => {
            if (button.getAttribute('data-filter') === filter) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        
        // Clear date filter when changing main filter
        this.clearDateFilter();
        
        // Reload schedule
        this.loadSchedule();
    }
    
    /**
     * Set the current date filter
     * @param {string} dateFilter - Date filter type
     */
    setDateFilter(dateFilter) {
        this.currentDateFilter = dateFilter;
        
        // Update date button states
        this.dateButtons.forEach(button => {
            if (button.getAttribute('data-date') === dateFilter) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        
        // Reload schedule
        this.loadSchedule();
    }
    
    /**
     * Clear date filter
     */
    clearDateFilter() {
        this.currentDateFilter = null;
        this.dateButtons.forEach(button => {
            button.classList.remove('active');
        });
    }
    
    /**
     * Load and display schedule
     */
    loadSchedule() {
        if (!this.currentTournament) {
            this.scheduleContainer.innerHTML = '<div class="no-tournament">Please select a tournament</div>';
            return;
        }
        
        // Get matches for current tournament
        let matches = getMatchesByTournament(this.currentTournament.id);
        
        // Apply status filter
        if (this.currentFilter !== 'all') {
            matches = matches.filter(match => match.status === this.currentFilter);
        }
        
        // Apply date filter
        if (this.currentDateFilter) {
            matches = this.applyDateFilter(matches, this.currentDateFilter);
        }
        
        // Sort matches by scheduled time
        matches.sort((a, b) => new Date(a.scheduledTime) - new Date(b.scheduledTime));
        
        // Enrich match data
        matches = matches.map(enrichMatchData);
        
        this.renderSchedule(matches);
    }
    
    /**
     * Apply date filter to matches
     * @param {array} matches - Array of matches
     * @param {string} dateFilter - Date filter type
     * @returns {array} Filtered matches
     */
    applyDateFilter(matches, dateFilter) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
        const weekEnd = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
        
        switch (dateFilter) {
            case 'today':
                return matches.filter(match => {
                    const matchDate = new Date(match.scheduledTime);
                    return matchDate >= today && matchDate < tomorrow;
                });
            case 'tomorrow':
                const dayAfterTomorrow = new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000);
                return matches.filter(match => {
                    const matchDate = new Date(match.scheduledTime);
                    return matchDate >= tomorrow && matchDate < dayAfterTomorrow;
                });
            case 'week':
                return matches.filter(match => {
                    const matchDate = new Date(match.scheduledTime);
                    return matchDate >= today && matchDate < weekEnd;
                });
            default:
                return matches;
        }
    }
    
    /**
     * Render schedule timeline
     * @param {array} matches - Array of match data
     */
    renderSchedule(matches) {
        if (matches.length === 0) {
            this.scheduleContainer.innerHTML = `
                <div class="no-matches">
                    <p>No matches found for the selected filters</p>
                </div>
            `;
            return;
        }
        
        // Group matches by date
        const matchesByDate = this.groupMatchesByDate(matches);
        
        // Render grouped matches
        this.scheduleContainer.innerHTML = Object.keys(matchesByDate).map(date => {
            const dayMatches = matchesByDate[date];
            const dateObj = new Date(date);
            
            return `
                <div class="schedule-day">
                    <div class="schedule-day-header">
                        <div class="schedule-day-title">${this.formatDayTitle(dateObj)}</div>
                        <div class="schedule-day-date">${formatDate(dateObj.toISOString(), 'date')}</div>
                    </div>
                    <div class="schedule-matches">
                        ${dayMatches.map(match => this.createMatchCardHTML(match)).join('')}
                    </div>
                </div>
            `;
        }).join('');
        
        this.addScheduleAnimations();
    }
    
    /**
     * Group matches by date
     * @param {array} matches - Array of matches
     * @returns {object} Matches grouped by date
     */
    groupMatchesByDate(matches) {
        const grouped = {};
        
        matches.forEach(match => {
            const matchDate = new Date(match.scheduledTime);
            const dateKey = matchDate.toISOString().split('T')[0]; // YYYY-MM-DD
            
            if (!grouped[dateKey]) {
                grouped[dateKey] = [];
            }
            grouped[dateKey].push(match);
        });
        
        return grouped;
    }
    
    /**
     * Format day title with relative dates
     * @param {Date} date - Date object
     * @returns {string} Formatted day title
     */
    formatDayTitle(date) {
        if (isToday(date.toISOString())) {
            return 'Today';
        } else if (isTomorrow(date.toISOString())) {
            return 'Tomorrow';
        } else {
            return getDayOfWeek(date.toISOString());
        }
    }
    
    /**
     * Create match card HTML for schedule
     * @param {object} match - Match data
     * @returns {string} HTML string
     */
    createMatchCardHTML(match) {
        // Use the existing match card component
        const tempDiv = document.createElement('div');
        const matchCard = createMatchCard(match);
        tempDiv.appendChild(matchCard);
        return tempDiv.innerHTML;
    }
    
    /**
     * Add animations to schedule elements
     */
    addScheduleAnimations() {
        const days = document.querySelectorAll('.schedule-day');
        
        days.forEach((day, index) => {
            day.style.animationDelay = `${index * 0.1}s`;
            day.classList.add('fade-in');
        });
    }
    
    /**
     * Export schedule data (for potential future use)
     * @returns {string} JSON string of schedule data
     */
    exportScheduleData() {
        if (!this.currentTournament) return null;
        
        const matches = getMatchesByTournament(this.currentTournament.id);
        const enrichedMatches = matches.map(enrichMatchData);
        
        return JSON.stringify({
            tournament: this.currentTournament,
            matches: enrichedMatches
        }, null, 2);
    }
}

// Initialize the schedule page when script loads
let schedulePage;

// Wait for all dependencies to load
function initSchedulePage() {
    // Check if all required functions are available
    if (typeof TOURNAMENTS !== 'undefined' && 
        typeof getMatchesByTournament === 'function' &&
        typeof enrichMatchData === 'function' &&
        typeof createMatchCard === 'function') {
        schedulePage = new SchedulePage();
    } else {
        // Retry after a short delay
        setTimeout(initSchedulePage, 100);
    }
}

// Start initialization
initSchedulePage();

// Export for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SchedulePage };
}
