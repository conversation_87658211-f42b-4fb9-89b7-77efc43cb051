/**
 * TEAM CARD COMPONENT
 * 
 * This file contains the TeamCard component for displaying team information
 * in a consistent, reusable format across the application.
 */

class TeamCard {
    constructor(teamData, includeStats = false) {
        this.team = teamData;
        this.includeStats = includeStats;
        this.element = null;
        this.stats = null;
        
        if (includeStats) {
            this.stats = getTeamStats(teamData.id);
        }
    }
    
    /**
     * Create the team card HTML element
     * @returns {HTMLElement} Team card element
     */
    createElement() {
        const card = document.createElement('div');
        card.className = 'team-card';
        card.setAttribute('data-team-id', this.team.id);
        card.setAttribute('data-region', this.team.region);
        
        card.innerHTML = this.getCardHTML();
        
        // Add click event listener
        card.addEventListener('click', () => this.handleCardClick());
        
        this.element = card;
        return card;
    }
    
    /**
     * Generate the HTML content for the team card
     * @returns {string} HTML string
     */
    getCardHTML() {
        return `
            <div class="team-card-logo">
                <img src="${this.getTeamLogo()}" 
                     alt="${this.team.name}" 
                     onerror="this.src='assets/images/team-logos/default.png'">
            </div>
            
            <div class="team-card-name">${this.team.name}</div>
            <div class="team-card-region">${this.team.region}</div>
            
            ${this.getStatsHTML()}
            
            <div class="team-players">
                ${this.getPlayersHTML()}
            </div>
            
            ${this.getSocialLinksHTML()}
        `;
    }
    
    /**
     * Get team logo URL
     * @returns {string} Logo URL
     */
    getTeamLogo() {
        return getTeamLogoUrl(this.team.id);
    }
    
    /**
     * Generate players HTML
     * @returns {string} Players HTML
     */
    getPlayersHTML() {
        if (!this.team.players || this.team.players.length === 0) {
            return '<div class="no-players">No roster information available</div>';
        }
        
        return this.team.players.map(player => `
            <div class="player">
                <span class="player-name">${player.name}</span>
                <span class="player-role">${player.role}</span>
            </div>
        `).join('');
    }
    
    /**
     * Generate statistics HTML
     * @returns {string} Stats HTML
     */
    getStatsHTML() {
        if (!this.includeStats || !this.stats) {
            return '';
        }
        
        return `
            <div class="team-stats">
                <div class="stat-item">
                    <span class="stat-label">Matches</span>
                    <span class="stat-value">${this.stats.matchesPlayed}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Win Rate</span>
                    <span class="stat-value">${this.stats.winRate}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Map W/L</span>
                    <span class="stat-value">${this.stats.mapsWon}/${this.stats.mapsLost}</span>
                </div>
            </div>
        `;
    }
    
    /**
     * Generate social links HTML
     * @returns {string} Social links HTML
     */
    getSocialLinksHTML() {
        if (!this.team.social) {
            return '';
        }
        
        const socialLinks = [];
        
        if (this.team.social.twitter) {
            socialLinks.push(`
                <a href="${this.team.social.twitter}" target="_blank" class="social-link twitter">
                    <span>Twitter</span>
                </a>
            `);
        }
        
        if (this.team.social.instagram) {
            socialLinks.push(`
                <a href="${this.team.social.instagram}" target="_blank" class="social-link instagram">
                    <span>Instagram</span>
                </a>
            `);
        }
        
        if (this.team.website) {
            socialLinks.push(`
                <a href="${this.team.website}" target="_blank" class="social-link website">
                    <span>Website</span>
                </a>
            `);
        }
        
        if (socialLinks.length === 0) {
            return '';
        }
        
        return `
            <div class="team-social">
                ${socialLinks.join('')}
            </div>
        `;
    }
    
    /**
     * Handle card click event
     */
    handleCardClick() {
        console.log('Team card clicked:', this.team.id);
        this.showTeamDetails();
    }
    
    /**
     * Show team details (placeholder for future implementation)
     */
    showTeamDetails() {
        // This could open a modal with detailed team information
        // For now, just log the team data
        console.log('Team details:', this.team);
        
        // Example implementation:
        // const modal = new TeamDetailsModal(this.team);
        // modal.show();
    }
    
    /**
     * Update the team card with new data
     * @param {object} newTeamData - Updated team data
     */
    update(newTeamData) {
        this.team = newTeamData;
        if (this.includeStats) {
            this.stats = getTeamStats(newTeamData.id);
        }
        if (this.element) {
            this.element.innerHTML = this.getCardHTML();
            this.element.setAttribute('data-region', this.team.region);
        }
    }
    
    /**
     * Get the card element
     * @returns {HTMLElement} Card element
     */
    getElement() {
        return this.element || this.createElement();
    }
}

/**
 * Create a team card from team data
 * @param {object} teamData - Team data object
 * @param {boolean} includeStats - Whether to include statistics
 * @returns {HTMLElement} Team card element
 */
function createTeamCard(teamData, includeStats = false) {
    const teamCard = new TeamCard(teamData, includeStats);
    return teamCard.createElement();
}

/**
 * Create multiple team cards
 * @param {array} teamsData - Array of team data objects
 * @param {boolean} includeStats - Whether to include statistics
 * @returns {array} Array of team card elements
 */
function createTeamCards(teamsData, includeStats = false) {
    return teamsData.map(teamData => createTeamCard(teamData, includeStats));
}

/**
 * Render team cards to a container
 * @param {HTMLElement} container - Container element
 * @param {array} teamsData - Array of team data objects
 * @param {boolean} includeStats - Whether to include statistics
 */
function renderTeamCards(container, teamsData, includeStats = false) {
    // Clear existing content
    container.innerHTML = '';
    
    if (teamsData.length === 0) {
        container.innerHTML = `
            <div class="no-teams">
                <p>No teams found</p>
            </div>
        `;
        return;
    }
    
    // Create and append team cards
    const cards = createTeamCards(teamsData, includeStats);
    cards.forEach(card => container.appendChild(card));
}

/**
 * Create team statistics table
 * @param {array} teamsData - Array of team data objects
 * @returns {HTMLElement} Statistics table element
 */
function createTeamStatsTable(teamsData) {
    const table = document.createElement('div');
    table.className = 'team-stats-table';
    
    // Create header
    const header = document.createElement('div');
    header.className = 'stats-header';
    header.innerHTML = `
        <div class="stat-cell team-cell">Team</div>
        <div class="stat-cell">Matches</div>
        <div class="stat-cell">Wins</div>
        <div class="stat-cell">Losses</div>
        <div class="stat-cell">Win Rate</div>
        <div class="stat-cell">Map W/L</div>
    `;
    table.appendChild(header);
    
    // Create rows for each team
    teamsData.forEach(team => {
        const stats = getTeamStats(team.id);
        const row = document.createElement('div');
        row.className = 'stats-row';
        row.innerHTML = `
            <div class="stat-cell team-cell">
                <img src="${getTeamLogoUrl(team.id)}" alt="${team.name}" class="team-logo-small">
                <span class="team-name">${team.shortName || team.name}</span>
            </div>
            <div class="stat-cell">${stats.matchesPlayed}</div>
            <div class="stat-cell">${stats.wins}</div>
            <div class="stat-cell">${stats.losses}</div>
            <div class="stat-cell">${stats.winRate}%</div>
            <div class="stat-cell">${stats.mapsWon}/${stats.mapsLost}</div>
        `;
        table.appendChild(row);
    });
    
    return table;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TeamCard,
        createTeamCard,
        createTeamCards,
        renderTeamCards,
        createTeamStatsTable
    };
}
