/**
 * BRACKET COMPONENT
 * 
 * This file contains the Bracket component for displaying tournament brackets
 * in various formats (single elimination, double elimination, etc.).
 */

class Bracket {
    constructor(tournamentData) {
        this.tournament = tournamentData;
        this.matches = getMatchesByTournament(tournamentData.id);
        this.element = null;
    }
    
    /**
     * Create the bracket HTML element
     * @returns {HTMLElement} Bracket element
     */
    createElement() {
        const bracket = document.createElement('div');
        bracket.className = 'bracket';
        bracket.setAttribute('data-tournament-id', this.tournament.id);
        bracket.setAttribute('data-format', this.tournament.format);
        
        bracket.innerHTML = this.getBracketHTML();
        
        this.element = bracket;
        return bracket;
    }
    
    /**
     * Generate the HTML content for the bracket
     * @returns {string} HTML string
     */
    getBracketHTML() {
        switch (this.tournament.format) {
            case 'single-elimination':
                return this.getSingleEliminationHTML();
            case 'double-elimination':
                return this.getDoubleEliminationHTML();
            case 'round-robin':
                return this.getRoundRobinHTML();
            case 'swiss':
                return this.getSwissHTML();
            default:
                return this.getSingleEliminationHTML();
        }
    }
    
    /**
     * Generate single elimination bracket HTML
     * @returns {string} HTML string
     */
    getSingleEliminationHTML() {
        if (!this.tournament.bracket || !this.tournament.bracket.rounds) {
            return '<div class="no-bracket">Bracket not available</div>';
        }
        
        const rounds = this.tournament.bracket.rounds;
        
        return rounds.map(round => `
            <div class="bracket-round">
                <div class="bracket-round-title">${round.name}</div>
                <div class="bracket-matches">
                    ${round.matches.map(match => this.getBracketMatchHTML(match)).join('')}
                </div>
            </div>
        `).join('');
    }
    
    /**
     * Generate double elimination bracket HTML
     * @returns {string} HTML string
     */
    getDoubleEliminationHTML() {
        if (!this.tournament.bracket || !this.tournament.bracket.rounds) {
            return '<div class="no-bracket">Bracket not available</div>';
        }
        
        const rounds = this.tournament.bracket.rounds;
        const upperBracket = rounds.filter(round => 
            round.name.toLowerCase().includes('upper') || 
            round.name.toLowerCase().includes('semifinal') ||
            round.name.toLowerCase().includes('final')
        );
        const lowerBracket = rounds.filter(round => 
            round.name.toLowerCase().includes('lower')
        );
        
        return `
            <div class="upper-bracket">
                <h4>Upper Bracket</h4>
                ${upperBracket.map(round => `
                    <div class="bracket-round">
                        <div class="bracket-round-title">${round.name}</div>
                        <div class="bracket-matches">
                            ${round.matches.map(match => this.getBracketMatchHTML(match)).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
            
            ${lowerBracket.length > 0 ? `
                <div class="lower-bracket">
                    <h4>Lower Bracket</h4>
                    ${lowerBracket.map(round => `
                        <div class="bracket-round">
                            <div class="bracket-round-title">${round.name}</div>
                            <div class="bracket-matches">
                                ${round.matches.map(match => this.getBracketMatchHTML(match)).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
        `;
    }
    
    /**
     * Generate round robin bracket HTML
     * @returns {string} HTML string
     */
    getRoundRobinHTML() {
        const standings = getTournamentStandings(this.tournament.id);
        
        return `
            <div class="round-robin-standings">
                <h4>Standings</h4>
                <div class="standings-table">
                    <div class="standings-header">
                        <div class="standing-cell">Pos</div>
                        <div class="standing-cell">Team</div>
                        <div class="standing-cell">Matches</div>
                        <div class="standing-cell">Wins</div>
                        <div class="standing-cell">Losses</div>
                        <div class="standing-cell">Win Rate</div>
                    </div>
                    ${standings.map((standing, index) => `
                        <div class="standings-row">
                            <div class="standing-cell">${index + 1}</div>
                            <div class="standing-cell team-cell">
                                <img src="${getTeamLogoUrl(standing.team.id)}" alt="${standing.team.name}" class="team-logo-small">
                                <span>${standing.team.shortName || standing.team.name}</span>
                            </div>
                            <div class="standing-cell">${standing.matchesPlayed}</div>
                            <div class="standing-cell">${standing.wins}</div>
                            <div class="standing-cell">${standing.losses}</div>
                            <div class="standing-cell">${standing.winRate}%</div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="round-robin-matches">
                <h4>All Matches</h4>
                <div class="matches-grid">
                    ${this.matches.map(match => this.getBracketMatchHTML(match)).join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * Generate Swiss format bracket HTML
     * @returns {string} HTML string
     */
    getSwissHTML() {
        // Group matches by round
        const matchesByRound = {};
        this.matches.forEach(match => {
            // Extract round from match ID or use a default grouping
            const roundKey = this.extractRoundFromMatch(match);
            if (!matchesByRound[roundKey]) {
                matchesByRound[roundKey] = [];
            }
            matchesByRound[roundKey].push(match);
        });
        
        const standings = getTournamentStandings(this.tournament.id);
        
        return `
            <div class="swiss-standings">
                <h4>Current Standings</h4>
                <div class="standings-table">
                    <div class="standings-header">
                        <div class="standing-cell">Pos</div>
                        <div class="standing-cell">Team</div>
                        <div class="standing-cell">Record</div>
                        <div class="standing-cell">Win Rate</div>
                    </div>
                    ${standings.map((standing, index) => `
                        <div class="standings-row">
                            <div class="standing-cell">${index + 1}</div>
                            <div class="standing-cell team-cell">
                                <img src="${getTeamLogoUrl(standing.team.id)}" alt="${standing.team.name}" class="team-logo-small">
                                <span>${standing.team.shortName || standing.team.name}</span>
                            </div>
                            <div class="standing-cell">${standing.wins}-${standing.losses}</div>
                            <div class="standing-cell">${standing.winRate}%</div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="swiss-rounds">
                ${Object.keys(matchesByRound).map(round => `
                    <div class="swiss-round">
                        <h4>${round}</h4>
                        <div class="matches-grid">
                            ${matchesByRound[round].map(match => this.getBracketMatchHTML(match)).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    /**
     * Generate HTML for a single bracket match
     * @param {object} matchData - Match data or match reference
     * @returns {string} HTML string
     */
    getBracketMatchHTML(matchData) {
        // Handle both actual match data and bracket references
        let match;
        if (typeof matchData === 'string' || matchData.id) {
            // This is a match reference, find the actual match
            const matchId = typeof matchData === 'string' ? matchData : matchData.id;
            match = this.matches.find(m => m.id === matchId);
        } else {
            match = matchData;
        }
        
        if (!match) {
            // Handle placeholder matches (like "winner-qf1")
            return this.getPlaceholderMatchHTML(matchData);
        }
        
        const enrichedMatch = enrichMatchData(match);
        const team1 = enrichedMatch.team1Data;
        const team2 = enrichedMatch.team2Data;
        
        return `
            <div class="bracket-match" data-match-id="${match.id}">
                <div class="bracket-team ${this.getBracketTeamClass(match, team1.id)}">
                    <div class="bracket-team-info">
                        <img src="${getTeamLogoUrl(team1.id)}" alt="${team1.name}" class="team-logo-small">
                        <span class="bracket-team-name">${team1.shortName || team1.name}</span>
                    </div>
                    <div class="bracket-team-score">${this.getTeamScore(match, team1.id)}</div>
                </div>
                
                <div class="bracket-team ${this.getBracketTeamClass(match, team2.id)}">
                    <div class="bracket-team-info">
                        <img src="${getTeamLogoUrl(team2.id)}" alt="${team2.name}" class="team-logo-small">
                        <span class="bracket-team-name">${team2.shortName || team2.name}</span>
                    </div>
                    <div class="bracket-team-score">${this.getTeamScore(match, team2.id)}</div>
                </div>
                
                <div class="bracket-match-info">
                    <div class="match-status">${this.getMatchStatus(match)}</div>
                    <div class="match-time">${formatDate(match.scheduledTime, 'short')}</div>
                </div>
            </div>
        `;
    }
    
    /**
     * Generate HTML for placeholder matches
     * @param {object} matchData - Match reference data
     * @returns {string} HTML string
     */
    getPlaceholderMatchHTML(matchData) {
        const team1Name = this.resolvePlaceholderTeam(matchData.team1);
        const team2Name = this.resolvePlaceholderTeam(matchData.team2);
        
        return `
            <div class="bracket-match placeholder">
                <div class="bracket-team">
                    <div class="bracket-team-info">
                        <span class="bracket-team-name">${team1Name}</span>
                    </div>
                    <div class="bracket-team-score">-</div>
                </div>
                
                <div class="bracket-team">
                    <div class="bracket-team-info">
                        <span class="bracket-team-name">${team2Name}</span>
                    </div>
                    <div class="bracket-team-score">-</div>
                </div>
                
                <div class="bracket-match-info">
                    <div class="match-status">TBD</div>
                </div>
            </div>
        `;
    }
    
    /**
     * Resolve placeholder team names
     * @param {string} placeholder - Placeholder string like "winner-qf1"
     * @returns {string} Resolved team name or placeholder
     */
    resolvePlaceholderTeam(placeholder) {
        if (!placeholder || !placeholder.includes('-')) {
            return placeholder;
        }
        
        // Try to find the actual winner
        const [type, matchRef] = placeholder.split('-');
        const referencedMatch = this.matches.find(m => m.id.includes(matchRef));
        
        if (referencedMatch && referencedMatch.result && referencedMatch.result.winner) {
            const winnerTeam = getTeamById(referencedMatch.result.winner);
            return winnerTeam ? winnerTeam.shortName || winnerTeam.name : placeholder;
        }
        
        return placeholder.replace('-', ' ').toUpperCase();
    }
    
    /**
     * Get bracket team class based on match result
     * @param {object} match - Match data
     * @param {string} teamId - Team ID
     * @returns {string} CSS class
     */
    getBracketTeamClass(match, teamId) {
        if (match.status === 'finished' && match.result) {
            if (match.result.winner === teamId) {
                return 'winner';
            } else {
                return 'loser';
            }
        }
        return '';
    }
    
    /**
     * Get team score for bracket display
     * @param {object} match - Match data
     * @param {string} teamId - Team ID
     * @returns {string} Score string
     */
    getTeamScore(match, teamId) {
        if (match.status === 'finished' && match.result) {
            if (match.team1 === teamId) {
                return match.result.score.team1.toString();
            } else if (match.team2 === teamId) {
                return match.result.score.team2.toString();
            }
        }
        return '-';
    }
    
    /**
     * Get match status for bracket display
     * @param {object} match - Match data
     * @returns {string} Status string
     */
    getMatchStatus(match) {
        const statusMap = {
            'live': 'LIVE',
            'finished': 'FINISHED',
            'upcoming': 'UPCOMING',
            'cancelled': 'CANCELLED',
            'postponed': 'POSTPONED'
        };
        
        return statusMap[match.status] || match.status.toUpperCase();
    }
    
    /**
     * Extract round information from match
     * @param {object} match - Match data
     * @returns {string} Round name
     */
    extractRoundFromMatch(match) {
        // Try to extract round from match ID
        if (match.id.includes('r1')) return 'Round 1';
        if (match.id.includes('r2')) return 'Round 2';
        if (match.id.includes('r3')) return 'Round 3';
        if (match.id.includes('r4')) return 'Round 4';
        if (match.id.includes('r5')) return 'Round 5';
        
        // Default grouping
        return 'Matches';
    }
    
    /**
     * Get the bracket element
     * @returns {HTMLElement} Bracket element
     */
    getElement() {
        return this.element || this.createElement();
    }
}

/**
 * Create a bracket from tournament data
 * @param {object} tournamentData - Tournament data object
 * @returns {HTMLElement} Bracket element
 */
function createBracket(tournamentData) {
    const bracket = new Bracket(tournamentData);
    return bracket.createElement();
}

/**
 * Render bracket to a container
 * @param {HTMLElement} container - Container element
 * @param {object} tournamentData - Tournament data object
 */
function renderBracket(container, tournamentData) {
    // Clear existing content
    container.innerHTML = '';
    
    if (!tournamentData) {
        container.innerHTML = '<div class="no-tournament">No tournament selected</div>';
        return;
    }
    
    // Create and append bracket
    const bracket = createBracket(tournamentData);
    container.appendChild(bracket);
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        Bracket,
        createBracket,
        renderBracket
    };
}
