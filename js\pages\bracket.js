/**
 * BRACKET PAGE FUNCTIONALITY
 * 
 * This file contains all the JavaScript functionality for the bracket page,
 * including tournament selection and bracket visualization.
 */

class BracketPage {
    constructor() {
        this.currentTournament = null;
        this.bracketContainer = null;
        this.tournamentTabs = null;
        this.bracketTitle = null;
        this.tournamentFormat = null;
        this.tournamentStatus = null;
        this.tournamentInfo = null;
        
        this.init();
    }
    
    /**
     * Initialize the bracket page
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    /**
     * Set up the bracket page elements and event listeners
     */
    setup() {
        // Get DOM elements
        this.bracketContainer = document.getElementById('bracket-container');
        this.tournamentTabs = document.getElementById('tournament-tabs');
        this.bracketTitle = document.getElementById('bracket-title');
        this.tournamentFormat = document.getElementById('tournament-format');
        this.tournamentStatus = document.getElementById('tournament-status');
        this.tournamentInfo = document.getElementById('tournament-info');
        
        if (!this.bracketContainer || !this.tournamentTabs) {
            console.error('Required containers not found');
            return;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Load tournament tabs
        this.loadTournamentTabs();
        
        // Set default tournament
        this.setDefaultTournament();
        
        // Set up navigation
        this.setupNavigation();
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Mobile navigation toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }
    }
    
    /**
     * Set up navigation highlighting
     */
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
    
    /**
     * Load tournament tabs
     */
    loadTournamentTabs() {
        // Filter tournaments that have bracket data or are suitable for brackets
        const bracketTournaments = TOURNAMENTS.filter(tournament => 
            tournament.format !== 'round-robin' || tournament.bracket
        ).sort((a, b) => {
            // Sort by status priority (ongoing > upcoming > completed)
            const statusPriority = { ongoing: 3, upcoming: 2, completed: 1, cancelled: 0 };
            return (statusPriority[b.status] || 0) - (statusPriority[a.status] || 0);
        });
        
        this.tournamentTabs.innerHTML = bracketTournaments.map(tournament => `
            <button class="tournament-tab" data-tournament-id="${tournament.id}">
                ${tournament.shortName || tournament.name}
                <span class="tournament-status">${tournament.status}</span>
            </button>
        `).join('');
        
        // Add event listeners to tournament tabs
        const tabs = this.tournamentTabs.querySelectorAll('.tournament-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tournamentId = e.target.getAttribute('data-tournament-id');
                this.setTournament(tournamentId);
            });
        });
    }
    
    /**
     * Set default tournament
     */
    setDefaultTournament() {
        // Find first ongoing tournament with bracket, then upcoming, then any
        let defaultTournament = TOURNAMENTS.find(t => t.status === 'ongoing' && t.bracket) ||
                               TOURNAMENTS.find(t => t.status === 'upcoming' && t.bracket) ||
                               TOURNAMENTS.find(t => t.bracket) ||
                               TOURNAMENTS[0];
        
        if (defaultTournament) {
            this.setTournament(defaultTournament.id);
        }
    }
    
    /**
     * Set the current tournament
     * @param {string} tournamentId - Tournament ID
     */
    setTournament(tournamentId) {
        this.currentTournament = getTournamentById(tournamentId);
        
        if (!this.currentTournament) return;
        
        // Update tournament tab states
        const tabs = this.tournamentTabs.querySelectorAll('.tournament-tab');
        tabs.forEach(tab => {
            if (tab.getAttribute('data-tournament-id') === tournamentId) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
        
        // Update tournament info
        this.updateTournamentInfo();
        
        // Load bracket for this tournament
        this.loadBracket();
    }
    
    /**
     * Update tournament information display
     */
    updateTournamentInfo() {
        if (!this.currentTournament) return;
        
        // Update title and format
        if (this.bracketTitle) {
            this.bracketTitle.textContent = `${this.currentTournament.name} Bracket`;
        }
        
        if (this.tournamentFormat) {
            this.tournamentFormat.textContent = this.formatTournamentFormat(this.currentTournament.format);
        }
        
        if (this.tournamentStatus) {
            this.tournamentStatus.textContent = this.currentTournament.status.toUpperCase();
            this.tournamentStatus.className = `tournament-status status-${this.currentTournament.status}`;
        }
        
        // Update tournament info panel
        if (this.tournamentInfo) {
            this.tournamentInfo.innerHTML = this.getTournamentInfoHTML();
        }
    }
    
    /**
     * Format tournament format for display
     * @param {string} format - Tournament format
     * @returns {string} Formatted string
     */
    formatTournamentFormat(format) {
        const formatMap = {
            'single-elimination': 'Single Elimination',
            'double-elimination': 'Double Elimination',
            'round-robin': 'Round Robin',
            'swiss': 'Swiss System'
        };
        
        return formatMap[format] || format;
    }
    
    /**
     * Get tournament info HTML
     * @returns {string} HTML string
     */
    getTournamentInfoHTML() {
        const tournament = this.currentTournament;
        const matches = getMatchesByTournament(tournament.id);
        const totalMatches = matches.length;
        const completedMatches = matches.filter(m => m.status === 'finished').length;
        const liveMatches = matches.filter(m => m.status === 'live').length;
        
        return `
            <div class="tournament-info-grid">
                <div class="tournament-info-item">
                    <span class="tournament-info-label">Prize Pool</span>
                    <span class="tournament-info-value">${tournament.prizePool || 'TBD'}</span>
                </div>
                <div class="tournament-info-item">
                    <span class="tournament-info-label">Location</span>
                    <span class="tournament-info-value">${tournament.location || 'TBD'}</span>
                </div>
                <div class="tournament-info-item">
                    <span class="tournament-info-label">Teams</span>
                    <span class="tournament-info-value">${tournament.teams.length}</span>
                </div>
                <div class="tournament-info-item">
                    <span class="tournament-info-label">Matches</span>
                    <span class="tournament-info-value">${completedMatches}/${totalMatches}</span>
                </div>
                <div class="tournament-info-item">
                    <span class="tournament-info-label">Format</span>
                    <span class="tournament-info-value">${tournament.rules?.matchFormat || 'BO3'}</span>
                </div>
                <div class="tournament-info-item">
                    <span class="tournament-info-label">Duration</span>
                    <span class="tournament-info-value">${this.getTournamentDuration()}</span>
                </div>
            </div>
            
            ${tournament.description ? `
                <div class="tournament-description">
                    <h4>About</h4>
                    <p>${tournament.description}</p>
                </div>
            ` : ''}
            
            ${liveMatches > 0 ? `
                <div class="live-matches-info">
                    <h4>Live Now</h4>
                    <p>${liveMatches} match${liveMatches === 1 ? '' : 'es'} currently in progress</p>
                </div>
            ` : ''}
        `;
    }
    
    /**
     * Get tournament duration string
     * @returns {string} Duration string
     */
    getTournamentDuration() {
        const tournament = this.currentTournament;
        if (!tournament.startDate || !tournament.endDate) return 'TBD';
        
        const start = new Date(tournament.startDate);
        const end = new Date(tournament.endDate);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
        
        return `${diffDays} day${diffDays === 1 ? '' : 's'}`;
    }
    
    /**
     * Load and display bracket
     */
    loadBracket() {
        if (!this.currentTournament) {
            this.bracketContainer.innerHTML = '<div class="no-tournament">Please select a tournament</div>';
            return;
        }
        
        // Show loading state
        this.bracketContainer.innerHTML = '<div class="loading">Loading bracket...</div>';
        
        // Simulate slight delay for better UX
        setTimeout(() => {
            try {
                renderBracket(this.bracketContainer, this.currentTournament);
                this.addBracketAnimations();
            } catch (error) {
                console.error('Error rendering bracket:', error);
                this.bracketContainer.innerHTML = `
                    <div class="bracket-error">
                        <p>Error loading bracket. Please try again.</p>
                    </div>
                `;
            }
        }, 200);
    }
    
    /**
     * Add animations to bracket elements
     */
    addBracketAnimations() {
        const rounds = document.querySelectorAll('.bracket-round');
        const matches = document.querySelectorAll('.bracket-match');
        
        rounds.forEach((round, index) => {
            round.style.animationDelay = `${index * 0.2}s`;
            round.classList.add('fade-in');
        });
        
        matches.forEach((match, index) => {
            match.style.animationDelay = `${index * 0.05}s`;
            match.classList.add('fade-in');
        });
    }
    
    /**
     * Refresh bracket data (for live updates)
     */
    refreshBracket() {
        if (this.currentTournament) {
            this.loadBracket();
        }
    }
    
    /**
     * Export bracket data (for potential future use)
     * @returns {string} JSON string of bracket data
     */
    exportBracketData() {
        if (!this.currentTournament) return null;
        
        const matches = getMatchesByTournament(this.currentTournament.id);
        const enrichedMatches = matches.map(enrichMatchData);
        
        return JSON.stringify({
            tournament: this.currentTournament,
            matches: enrichedMatches,
            standings: getTournamentStandings(this.currentTournament.id)
        }, null, 2);
    }
    
    /**
     * Handle bracket match click (for potential future features)
     * @param {string} matchId - Match ID
     */
    handleMatchClick(matchId) {
        console.log('Bracket match clicked:', matchId);
        // Could implement match details modal or navigation
    }
}

// Initialize the bracket page when script loads
let bracketPage;

// Wait for all dependencies to load
function initBracketPage() {
    // Check if all required functions are available
    if (typeof TOURNAMENTS !== 'undefined' && 
        typeof getTournamentById === 'function' &&
        typeof getMatchesByTournament === 'function' &&
        typeof renderBracket === 'function' &&
        typeof getTournamentStandings === 'function') {
        bracketPage = new BracketPage();
    } else {
        // Retry after a short delay
        setTimeout(initBracketPage, 100);
    }
}

// Start initialization
initBracketPage();

// Export for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BracketPage };
}
