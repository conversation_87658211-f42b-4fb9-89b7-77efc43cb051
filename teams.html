<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teams - Valorant Tournament Hub</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/valorant-theme.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>VTH</h1>
                <span>Valorant Tournament Hub</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="teams.html" class="nav-link active">Teams</a></li>
                <li><a href="schedule.html" class="nav-link">Schedule</a></li>
                <li><a href="bracket.html" class="nav-link">Brackets</a></li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1 class="hero-title">Teams</h1>
                <p class="hero-subtitle">Meet the competing teams and their rosters</p>
            </div>
        </section>

        <!-- Teams Section -->
        <section class="teams-section">
            <div class="container">
                <!-- Filters -->
                <div class="section-header">
                    <h2>All Teams</h2>
                    <div class="team-filters">
                        <div class="filter-tabs">
                            <button class="filter-btn active" data-filter="all">All Regions</button>
                            <button class="filter-btn" data-filter="NA">NA</button>
                            <button class="filter-btn" data-filter="EU">EU</button>
                            <button class="filter-btn" data-filter="APAC">APAC</button>
                            <button class="filter-btn" data-filter="BR">BR</button>
                            <button class="filter-btn" data-filter="KR">KR</button>
                        </div>
                        <div class="search-container">
                            <input type="text" id="team-search" placeholder="Search teams..." class="search-input">
                        </div>
                    </div>
                </div>

                <!-- Teams Grid -->
                <div id="teams-container" class="teams-grid">
                    <!-- Teams will be dynamically loaded here -->
                </div>

                <!-- Team Statistics -->
                <div class="team-stats-section">
                    <h3>Team Statistics</h3>
                    <div id="team-stats-container" class="stats-grid">
                        <!-- Team stats will be loaded here -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Valorant Tournament Hub. Built for the community.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data/teams.js"></script>
    <script src="js/data/tournaments.js"></script>
    <script src="js/data/matches.js"></script>
    <script src="js/utils/dateUtils.js"></script>
    <script src="js/utils/dataManager.js"></script>
    <script src="js/components/teamCard.js"></script>
    <script src="js/pages/teams.js"></script>
</body>
</html>
