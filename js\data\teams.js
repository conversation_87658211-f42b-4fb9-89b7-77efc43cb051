/**
 * TEAMS DATA CONFIGURATION
 * 
 * This file contains all team information for the tournament system.
 * Each team has a unique ID that can be referenced in tournaments and matches.
 * 
 * HOW TO ADD A NEW TEAM:
 * 1. Add a new object to the TEAMS array
 * 2. Give it a unique ID (use lowercase, no spaces)
 * 3. Fill in all required fields
 * 4. Add team logo to assets/images/team-logos/ folder
 * 
 * TEAM OBJECT STRUCTURE:
 * {
 *   id: "unique-team-id",           // Used for referencing in matches
 *   name: "Team Display Name",      // Full team name
 *   shortName: "TDN",              // 3-4 letter abbreviation
 *   region: "NA/EU/APAC/etc",      // Team's region
 *   logo: "team-logo.png",         // Logo filename (in assets/images/team-logos/)
 *   players: [                     // Array of player objects
 *     {
 *       name: "Player Name",       // Player's in-game name
 *       role: "Duelist/Controller/Initiator/Sentinel" // Primary role
 *     }
 *   ],
 *   founded: "2023",               // Year team was founded
 *   website: "https://...",        // Optional: team website
 *   social: {                      // Optional: social media links
 *     twitter: "https://twitter.com/...",
 *     instagram: "https://instagram.com/..."
 *   }
 * }
 */

const TEAMS = [
    {
        id: "sentinels",
        name: "Sentinels",
        shortName: "SEN",
        region: "NA",
        logo: "sentinels.png",
        players: [
            { name: "TenZ", role: "Duelist" },
            { name: "Zekken", role: "Duelist" },
            { name: "Sacy", role: "Initiator" },
            { name: "pANcada", role: "Controller" },
            { name: "johnqt", role: "Sentinel" }
        ],
        founded: "2020",
        website: "https://sentinels.gg",
        social: {
            twitter: "https://twitter.com/Sentinels",
            instagram: "https://instagram.com/sentinels"
        }
    },
    {
        id: "fnatic",
        name: "Fnatic",
        shortName: "FNC",
        region: "EU",
        logo: "fnatic.png",
        players: [
            { name: "Derke", role: "Duelist" },
            { name: "Alfajer", role: "Sentinel" },
            { name: "Chronicle", role: "Initiator" },
            { name: "Leo", role: "Initiator" },
            { name: "Boaster", role: "Controller" }
        ],
        founded: "2021",
        website: "https://fnatic.com",
        social: {
            twitter: "https://twitter.com/FNATIC",
            instagram: "https://instagram.com/fnatic"
        }
    },
    {
        id: "loud",
        name: "LOUD",
        shortName: "LOU",
        region: "BR",
        logo: "loud.png",
        players: [
            { name: "aspas", role: "Duelist" },
            { name: "Less", role: "Sentinel" },
            { name: "Saadhak", role: "Initiator" },
            { name: "Cauanzin", role: "Initiator" },
            { name: "tuyz", role: "Controller" }
        ],
        founded: "2022",
        website: "https://loud.gg",
        social: {
            twitter: "https://twitter.com/LOUDgg",
            instagram: "https://instagram.com/loud"
        }
    },
    {
        id: "paper-rex",
        name: "Paper Rex",
        shortName: "PRX",
        region: "APAC",
        logo: "paper-rex.png",
        players: [
            { name: "f0rsakeN", role: "Duelist" },
            { name: "Jinggg", role: "Duelist" },
            { name: "d4v41", role: "Initiator" },
            { name: "mindfreak", role: "Controller" },
            { name: "something", role: "Duelist" }
        ],
        founded: "2021",
        website: "https://paperrex.gg",
        social: {
            twitter: "https://twitter.com/PaperRex",
            instagram: "https://instagram.com/paperrex"
        }
    },
    {
        id: "navi",
        name: "Natus Vincere",
        shortName: "NAVI",
        region: "EU",
        logo: "navi.png",
        players: [
            { name: "cNed", role: "Duelist" },
            { name: "Shao", role: "Initiator" },
            { name: "ANGE1", role: "Controller" },
            { name: "Suygetsu", role: "Sentinel" },
            { name: "Zyppan", role: "Initiator" }
        ],
        founded: "2021",
        website: "https://navi.gg",
        social: {
            twitter: "https://twitter.com/natusvincere",
            instagram: "https://instagram.com/natus_vincere_official"
        }
    },
    {
        id: "eg",
        name: "Evil Geniuses",
        shortName: "EG",
        region: "NA",
        logo: "eg.png",
        players: [
            { name: "Demon1", role: "Duelist" },
            { name: "jawgemo", role: "Duelist" },
            { name: "Ethan", role: "Initiator" },
            { name: "C0M", role: "Controller" },
            { name: "Boostio", role: "Initiator" }
        ],
        founded: "2022",
        website: "https://evilgeniuses.gg",
        social: {
            twitter: "https://twitter.com/EvilGeniuses",
            instagram: "https://instagram.com/evilgeniuses"
        }
    },
    {
        id: "drx",
        name: "DRX",
        shortName: "DRX",
        region: "KR",
        logo: "drx.png",
        players: [
            { name: "Rb", role: "Duelist" },
            { name: "Stax", role: "Initiator" },
            { name: "BuZz", role: "Duelist" },
            { name: "MaKo", role: "Controller" },
            { name: "Zest", role: "Sentinel" }
        ],
        founded: "2021",
        website: "https://drx.gg",
        social: {
            twitter: "https://twitter.com/DRX_VS",
            instagram: "https://instagram.com/drx_vs"
        }
    },
    {
        id: "nrg",
        name: "NRG Esports",
        shortName: "NRG",
        region: "NA",
        logo: "nrg.png",
        players: [
            { name: "Ardiis", role: "Duelist" },
            { name: "Victor", role: "Duelist" },
            { name: "Crashies", role: "Initiator" },
            { name: "Marved", role: "Controller" },
            { name: "FNS", role: "Sentinel" }
        ],
        founded: "2023",
        website: "https://nrg.gg",
        social: {
            twitter: "https://twitter.com/NRGgg",
            instagram: "https://instagram.com/nrggg"
        }
    }
];

/**
 * UTILITY FUNCTIONS FOR TEAMS
 */

// Get team by ID
function getTeamById(teamId) {
    return TEAMS.find(team => team.id === teamId);
}

// Get teams by region
function getTeamsByRegion(region) {
    return TEAMS.filter(team => team.region === region);
}

// Get all regions
function getAllRegions() {
    return [...new Set(TEAMS.map(team => team.region))];
}

// Get team logo URL
function getTeamLogoUrl(teamId) {
    const team = getTeamById(teamId);
    return team ? `assets/images/team-logos/${team.logo}` : 'assets/images/team-logos/default.png';
}

// Search teams by name
function searchTeams(query) {
    const lowercaseQuery = query.toLowerCase();
    return TEAMS.filter(team => 
        team.name.toLowerCase().includes(lowercaseQuery) ||
        team.shortName.toLowerCase().includes(lowercaseQuery)
    );
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TEAMS,
        getTeamById,
        getTeamsByRegion,
        getAllRegions,
        getTeamLogoUrl,
        searchTeams
    };
}
