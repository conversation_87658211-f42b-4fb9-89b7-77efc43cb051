# Valorant Tournament Hub

A static website for managing Valorant tournaments with a clean, Valorant-themed UI. Built with vanilla HTML, CSS, and JavaScript for easy manual updates and deployment.

## 🎯 Features

- **Upcoming Matches Display** - Shows live and upcoming matches on the home page
- **Team Management** - Centralized team data with reusable team information
- **Tournament System** - Support for multiple tournament formats (single/double elimination, round-robin, Swiss)
- **Bracket Visualization** - Interactive tournament brackets
- **Responsive Design** - Mobile-friendly layout
- **Valorant Theme** - Dark UI with red accents matching Valorant's aesthetic

## 📁 Project Structure

```
/
├── index.html              # Home page (upcoming matches)
├── teams.html              # Teams page
├── schedule.html           # Full schedule
├── bracket.html            # Tournament brackets
├── css/
│   ├── main.css           # Global styles
│   ├── valorant-theme.css # Valorant color scheme
│   └── components.css     # Reusable components
├── js/
│   ├── data/
│   │   ├── teams.js       # Team definitions
│   │   ├── tournaments.js # Tournament data
│   │   └── matches.js     # Match data
│   ├── components/        # UI components
│   ├── pages/            # Page-specific logic
│   └── utils/            # Utility functions
├── assets/
│   └── images/
│       └── team-logos/   # Team logo storage
└── README.md
```

## 🔧 Manual Updates Guide

### Adding a New Team

1. Open `js/data/teams.js`
2. Add a new team object to the `TEAMS` array:

```javascript
{
    id: "team-id",                    // Unique identifier (lowercase, no spaces)
    name: "Team Name",                // Full team name
    shortName: "TN",                  // 3-4 letter abbreviation
    region: "NA",                     // Team's region
    logo: "team-logo.png",            // Logo filename
    players: [
        { name: "Player1", role: "Duelist" },
        { name: "Player2", role: "Controller" },
        // ... more players
    ],
    founded: "2023",
    website: "https://team-website.com",
    social: {
        twitter: "https://twitter.com/team",
        instagram: "https://instagram.com/team"
    }
}
```

3. Add the team logo to `assets/images/team-logos/`

### Creating a New Tournament

1. Open `js/data/tournaments.js`
2. Add a new tournament object to the `TOURNAMENTS` array:

```javascript
{
    id: "tournament-id",
    name: "Tournament Name",
    shortName: "TN",
    format: "single-elimination",     // single-elimination, double-elimination, round-robin, swiss
    status: "upcoming",               // upcoming, ongoing, completed, cancelled
    startDate: "2024-01-15",
    endDate: "2024-01-20",
    prizePool: "$100,000",
    location: "Location",
    teams: ["team-id-1", "team-id-2"], // Reference team IDs from teams.js
    description: "Tournament description",
    rules: {
        mapPool: ["Bind", "Haven", "Split"],
        matchFormat: "BO3",
        overtime: true
    }
}
```

### Adding Matches

1. Open `js/data/matches.js`
2. Add a new match object to the `MATCHES` array:

```javascript
{
    id: "match-id",
    tournamentId: "tournament-id",    // Reference tournament ID
    team1: "team-id-1",              // Reference team IDs
    team2: "team-id-2",
    scheduledTime: "2024-01-15T18:00:00Z", // ISO 8601 format
    status: "upcoming",               // upcoming, live, finished, cancelled, postponed
    format: "BO3",
    maps: ["Bind", "Haven", "Split"],
    stream: {
        platform: "Twitch",
        url: "https://twitch.tv/channel",
        language: "EN"
    }
}
```

### Updating Match Results

1. Open `js/data/matches.js`
2. Find the match by ID
3. Update the status to "finished"
4. Add the result object:

```javascript
result: {
    winner: "team-id",               // Winning team ID
    score: { team1: 2, team2: 1 },  // Map score
    mapResults: [
        { map: "Bind", team1Score: 13, team2Score: 11, winner: "team-id-1" },
        { map: "Haven", team1Score: 9, team2Score: 13, winner: "team-id-2" },
        { map: "Split", team1Score: 13, team2Score: 8, winner: "team-id-1" }
    ]
}
```

### Match Status Options

- `upcoming` - Match is scheduled but hasn't started
- `live` - Match is currently in progress
- `finished` - Match is completed with results
- `cancelled` - Match was cancelled
- `postponed` - Match was postponed to a later time

### Tournament Status Options

- `upcoming` - Tournament hasn't started yet
- `ongoing` - Tournament is currently running
- `completed` - Tournament is finished
- `cancelled` - Tournament was cancelled

## 🎨 Customization

### Colors

Edit `css/valorant-theme.css` to modify the color scheme:

```css
:root {
    --bg-primary: #0f1419;        /* Main background */
    --bg-secondary: #1a1f2e;      /* Secondary background */
    --accent-primary: #ff4655;    /* Valorant red accent */
    --text-primary: #ffffff;      /* Primary text */
    --text-secondary: #b8bcc8;    /* Secondary text */
}
```

### Adding Team Logos

1. Save team logos as PNG files in `assets/images/team-logos/`
2. Use the filename in the team's `logo` field
3. Recommended size: 200x200px or larger, square aspect ratio

## 🚀 Deployment

### GitHub Pages

1. Push your code to a GitHub repository
2. Go to repository Settings > Pages
3. Select source branch (usually `main`)
4. Your site will be available at `https://username.github.io/repository-name`

### Netlify

1. Connect your GitHub repository to Netlify
2. Set build command: (leave empty for static sites)
3. Set publish directory: `/` (root)
4. Deploy automatically on git push

### Manual Hosting

Upload all files to any web server that supports static HTML files.

## 📱 Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🔄 Live Updates

The site automatically refreshes live matches every 30 seconds when the page is visible. No manual refresh needed for live content.

## 🛠 Development

No build process required! Simply edit the files and refresh your browser to see changes.

### File Dependencies

- All data files (`teams.js`, `tournaments.js`, `matches.js`) must be loaded before page scripts
- Utility files (`dateUtils.js`, `dataManager.js`) must be loaded before components
- Component files must be loaded before page scripts

## 📝 Tips for Manual Updates

1. **Always use unique IDs** - Team IDs, tournament IDs, and match IDs must be unique
2. **Reference existing IDs** - When creating matches, always reference existing team and tournament IDs
3. **Use ISO date format** - All dates should be in ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ)
4. **Test after updates** - Always check the website after making changes
5. **Backup before major changes** - Keep a backup of your data files before making large updates

## 🐛 Troubleshooting

### Common Issues

1. **Teams not showing** - Check that team IDs are unique and properly formatted
2. **Matches not displaying** - Verify that team and tournament IDs in matches exist in their respective files
3. **Dates not formatting correctly** - Ensure dates are in ISO 8601 format
4. **Images not loading** - Check that image files exist in the correct directory and filenames match exactly

### Browser Console

Open browser developer tools (F12) and check the console for any JavaScript errors.

## 📄 License

This project is open source and available under the MIT License.
