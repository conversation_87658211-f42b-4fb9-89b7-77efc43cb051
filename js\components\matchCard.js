/**
 * MATCH CARD COMPONENT
 * 
 * This file contains the MatchCard component for displaying match information
 * in a consistent, reusable format across the application.
 */

class MatchCard {
    constructor(matchData) {
        this.match = matchData;
        this.element = null;
    }
    
    /**
     * Create the match card HTML element
     * @returns {HTMLElement} Match card element
     */
    createElement() {
        const card = document.createElement('div');
        card.className = 'match-card';
        card.setAttribute('data-match-id', this.match.id);
        card.setAttribute('data-status', this.match.status);
        
        card.innerHTML = this.getCardHTML();
        
        // Add click event listener
        card.addEventListener('click', () => this.handleCardClick());
        
        this.element = card;
        return card;
    }
    
    /**
     * Generate the HTML content for the match card
     * @returns {string} HTML string
     */
    getCardHTML() {
        const statusInfo = this.getStatusInfo();
        const timeInfo = this.getTimeInfo();
        const scoreInfo = this.getScoreInfo();
        
        return `
            <div class="match-header">
                <div class="match-status">
                    <span class="status-indicator status-${this.match.status}"></span>
                    ${statusInfo.text}
                </div>
                <div class="match-time">${timeInfo}</div>
            </div>
            
            <div class="match-teams">
                <div class="team ${this.getTeamClass(this.match.team1Data.id)}">
                    <div class="team-logo ${this.getTeamLogoClass(this.match.team1Data.id)}">
                        <img src="${this.getTeamLogo(this.match.team1Data.id)}" 
                             alt="${this.match.team1Data.name}" 
                             onerror="this.src='assets/images/team-logos/default.png'">
                    </div>
                    <div class="team-name">${this.match.team1Data.shortName || this.match.team1Data.name}</div>
                    ${scoreInfo.team1Score}
                </div>
                
                <div class="vs-divider">
                    <span class="vs-text">VS</span>
                </div>
                
                <div class="team ${this.getTeamClass(this.match.team2Data.id)}">
                    <div class="team-logo ${this.getTeamLogoClass(this.match.team2Data.id)}">
                        <img src="${this.getTeamLogo(this.match.team2Data.id)}" 
                             alt="${this.match.team2Data.name}"
                             onerror="this.src='assets/images/team-logos/default.png'">
                    </div>
                    <div class="team-name">${this.match.team2Data.shortName || this.match.team2Data.name}</div>
                    ${scoreInfo.team2Score}
                </div>
            </div>
            
            <div class="match-info">
                <div class="match-tournament">${this.match.tournamentData.shortName || this.match.tournamentData.name}</div>
                <div class="match-format">${this.match.format}</div>
                <div class="match-map">${this.getMapInfo()}</div>
            </div>
            
            ${this.getStreamInfo()}
        `;
    }
    
    /**
     * Get status information for display
     * @returns {object} Status info
     */
    getStatusInfo() {
        const status = getTimeBasedStatus(this.match.scheduledTime, this.match.status);
        
        const statusMap = {
            'LIVE': { text: 'LIVE', class: 'live' },
            'STARTING': { text: 'STARTING', class: 'live' },
            'STARTING SOON': { text: 'STARTING SOON', class: 'upcoming' },
            'UPCOMING': { text: 'UPCOMING', class: 'upcoming' },
            'FINISHED': { text: 'FINISHED', class: 'finished' },
            'CANCELLED': { text: 'CANCELLED', class: 'cancelled' },
            'POSTPONED': { text: 'POSTPONED', class: 'cancelled' }
        };
        
        return statusMap[status] || { text: status, class: 'upcoming' };
    }
    
    /**
     * Get formatted time information
     * @returns {string} Formatted time
     */
    getTimeInfo() {
        if (this.match.status === 'live') {
            return 'LIVE NOW';
        } else if (this.match.status === 'finished') {
            return formatDate(this.match.scheduledTime, 'date');
        } else {
            return formatMatchTime(this.match.scheduledTime);
        }
    }
    
    /**
     * Get score information for display
     * @returns {object} Score info
     */
    getScoreInfo() {
        if (this.match.status === 'finished' && this.match.result) {
            const score = this.match.result.score;
            return {
                team1Score: `<div class="team-score">${score.team1}</div>`,
                team2Score: `<div class="team-score">${score.team2}</div>`
            };
        } else {
            return {
                team1Score: '',
                team2Score: ''
            };
        }
    }
    
    /**
     * Get team class based on match result
     * @param {string} teamId - Team ID
     * @returns {string} CSS class
     */
    getTeamClass(teamId) {
        if (this.match.status === 'finished' && this.match.result) {
            if (this.match.result.winner === teamId) {
                return 'winner';
            } else {
                return 'loser';
            }
        }
        return '';
    }
    
    /**
     * Get team logo class based on match result
     * @param {string} teamId - Team ID
     * @returns {string} CSS class
     */
    getTeamLogoClass(teamId) {
        if (this.match.status === 'finished' && this.match.result) {
            if (this.match.result.winner === teamId) {
                return 'winner';
            } else {
                return 'loser';
            }
        }
        return '';
    }
    
    /**
     * Get team logo URL
     * @param {string} teamId - Team ID
     * @returns {string} Logo URL
     */
    getTeamLogo(teamId) {
        return getTeamLogoUrl(teamId);
    }
    
    /**
     * Get map information for display
     * @returns {string} Map info
     */
    getMapInfo() {
        if (this.match.maps && this.match.maps.length > 0) {
            if (this.match.maps.length === 1) {
                return this.match.maps[0];
            } else {
                return `${this.match.maps.length} Maps`;
            }
        }
        return this.match.format;
    }
    
    /**
     * Get stream information HTML
     * @returns {string} Stream info HTML
     */
    getStreamInfo() {
        if (this.match.stream && (this.match.status === 'live' || this.match.status === 'upcoming')) {
            return `
                <div class="match-stream">
                    <a href="${this.match.stream.url}" target="_blank" class="stream-link">
                        <span class="stream-platform">${this.match.stream.platform}</span>
                        <span class="stream-text">Watch Live</span>
                    </a>
                </div>
            `;
        }
        return '';
    }
    
    /**
     * Handle card click event
     */
    handleCardClick() {
        // You can implement navigation to match details page here
        console.log('Match card clicked:', this.match.id);
        
        // Example: Show match details modal or navigate to match page
        this.showMatchDetails();
    }
    
    /**
     * Show match details (placeholder for future implementation)
     */
    showMatchDetails() {
        // This could open a modal with detailed match information
        // For now, just log the match data
        console.log('Match details:', this.match);
        
        // Example implementation:
        // const modal = new MatchDetailsModal(this.match);
        // modal.show();
    }
    
    /**
     * Update the match card with new data
     * @param {object} newMatchData - Updated match data
     */
    update(newMatchData) {
        this.match = newMatchData;
        if (this.element) {
            this.element.innerHTML = this.getCardHTML();
            this.element.setAttribute('data-status', this.match.status);
        }
    }
    
    /**
     * Get the card element
     * @returns {HTMLElement} Card element
     */
    getElement() {
        return this.element || this.createElement();
    }
}

/**
 * Create a match card from match data
 * @param {object} matchData - Match data object
 * @returns {HTMLElement} Match card element
 */
function createMatchCard(matchData) {
    const enrichedMatch = enrichMatchData(matchData);
    const matchCard = new MatchCard(enrichedMatch);
    return matchCard.createElement();
}

/**
 * Create multiple match cards
 * @param {array} matchesData - Array of match data objects
 * @returns {array} Array of match card elements
 */
function createMatchCards(matchesData) {
    return matchesData.map(matchData => createMatchCard(matchData));
}

/**
 * Render match cards to a container
 * @param {HTMLElement} container - Container element
 * @param {array} matchesData - Array of match data objects
 */
function renderMatchCards(container, matchesData) {
    // Clear existing content
    container.innerHTML = '';
    
    if (matchesData.length === 0) {
        container.innerHTML = `
            <div class="no-matches">
                <p>No matches found</p>
            </div>
        `;
        return;
    }
    
    // Create and append match cards
    const cards = createMatchCards(matchesData);
    cards.forEach(card => container.appendChild(card));
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MatchCard,
        createMatchCard,
        createMatchCards,
        renderMatchCards
    };
}
