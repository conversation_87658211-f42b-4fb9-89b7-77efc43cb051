/* ===== MATCH CARD COMPONENT ===== */
.match-card {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.match-card:hover {
    border-color: var(--accent-primary);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.match-card:hover::before {
    transform: scaleX(1);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.match-status {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.match-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.team-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    transition: all var(--transition-normal);
}

.team-logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.team-logo.winner {
    border-color: var(--team-win);
    box-shadow: 0 0 15px rgba(104, 211, 145, 0.3);
}

.team-logo.loser {
    border-color: var(--team-loss);
    opacity: 0.6;
}

.team-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.team-score {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
}

.vs-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 1rem;
}

.vs-text {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.match-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.match-tournament {
    color: var(--text-secondary);
    font-weight: 500;
}

.match-map {
    color: var(--accent-primary);
    font-weight: 600;
}

/* ===== TEAM CARD COMPONENT ===== */
.team-card {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 2rem;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.team-card:hover {
    border-color: var(--accent-primary);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.team-card:hover::before {
    transform: scaleX(1);
}

.team-card-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    border: 3px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all var(--transition-normal);
}

.team-card-logo img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
}

.team-card:hover .team-card-logo {
    border-color: var(--accent-primary);
    box-shadow: var(--shadow-accent);
}

.team-card-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.team-card-region {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

.team-players {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.player {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
}

.player:hover {
    border-color: var(--accent-primary);
    background: rgba(255, 70, 85, 0.1);
}

.player-name {
    font-weight: 600;
    color: var(--text-primary);
}

.player-role {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== BRACKET COMPONENT ===== */
.bracket-container {
    overflow-x: auto;
    padding: 2rem 0;
}

.bracket {
    display: flex;
    gap: 4rem;
    min-width: max-content;
    padding: 2rem;
}

.bracket-round {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    min-width: 250px;
}

.bracket-round-title {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-primary);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 1rem;
}

.bracket-match {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    position: relative;
    transition: all var(--transition-normal);
}

.bracket-match:hover {
    border-color: var(--accent-primary);
    box-shadow: var(--shadow-md);
}

.bracket-team {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    margin-bottom: 0.5rem;
    transition: all var(--transition-fast);
}

.bracket-team:last-child {
    margin-bottom: 0;
}

.bracket-team.winner {
    background: rgba(104, 211, 145, 0.1);
    border: 1px solid var(--team-win);
}

.bracket-team.loser {
    background: rgba(252, 129, 129, 0.1);
    border: 1px solid var(--team-loss);
    opacity: 0.7;
}

.bracket-team-name {
    font-weight: 600;
    color: var(--text-primary);
}

.bracket-team-score {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--accent-primary);
}

/* ===== TOURNAMENT SELECTOR ===== */
.tournament-selector {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.tournament-selector h3 {
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.tournament-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tournament-tab {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all var(--transition-normal);
}

.tournament-tab:hover,
.tournament-tab.active {
    background: var(--accent-primary);
    color: var(--text-primary);
    border-color: var(--accent-primary);
    transform: translateY(-2px);
}

/* ===== ADDITIONAL COMPONENTS ===== */

/* Teams Grid */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

/* Team Statistics */
.team-stats {
    display: flex;
    justify-content: space-between;
    margin: 1rem 0;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-primary);
}

/* Team Statistics Table */
.team-stats-table {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.stats-header,
.stats-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    align-items: center;
}

.stats-header {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.stats-row {
    border-top: 1px solid var(--border-color);
    transition: background var(--transition-fast);
}

.stats-row:hover {
    background: var(--bg-tertiary);
}

.stat-cell {
    display: flex;
    align-items: center;
}

.team-cell {
    gap: 0.75rem;
}

.team-logo-small {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
}

/* Social Links */
.team-social {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    justify-content: center;
}

.social-link {
    padding: 0.5rem 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all var(--transition-normal);
}

.social-link:hover {
    background: var(--accent-primary);
    color: var(--text-primary);
    border-color: var(--accent-primary);
}

/* Search Input */
.search-container {
    position: relative;
}

.search-input {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-family: inherit;
    font-size: 1rem;
    width: 250px;
    transition: all var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(255, 70, 85, 0.2);
}

.search-input::placeholder {
    color: var(--text-tertiary);
}

/* Schedule Timeline */
.schedule-timeline {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.schedule-day {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.schedule-day-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.schedule-day-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

.schedule-day-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.schedule-matches {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
}

/* Date Filters */
.date-filters {
    display: flex;
    gap: 0.5rem;
}

.date-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all var(--transition-normal);
}

.date-btn:hover,
.date-btn.active {
    background: var(--accent-primary);
    color: var(--text-primary);
    border-color: var(--accent-primary);
}

/* Tournament Info Panel */
.tournament-info-panel {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 2rem;
    margin-top: 2rem;
    border: 1px solid var(--border-color);
}

.tournament-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.tournament-info-item {
    text-align: center;
}

.tournament-info-label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.tournament-info-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Loading States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.no-matches,
.no-teams,
.no-tournament,
.no-bracket {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE COMPONENTS ===== */
@media (max-width: 768px) {
    .match-teams {
        flex-direction: column;
        gap: 1rem;
    }

    .vs-divider {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .team-card {
        padding: 1.5rem;
    }

    .team-card-logo {
        width: 80px;
        height: 80px;
    }

    .team-card-logo img {
        width: 60px;
        height: 60px;
    }

    .bracket {
        gap: 2rem;
    }

    .bracket-round {
        min-width: 200px;
    }

    .tournament-tabs {
        justify-content: center;
    }

    .teams-grid {
        grid-template-columns: 1fr;
    }

    .search-input {
        width: 100%;
    }

    .stats-header,
    .stats-row {
        grid-template-columns: 2fr 1fr 1fr 1fr;
        font-size: 0.8rem;
    }

    .schedule-matches {
        grid-template-columns: 1fr;
    }

    .tournament-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .match-card,
    .team-card {
        padding: 1rem;
    }

    .team-logo {
        width: 50px;
        height: 50px;
    }

    .team-logo img {
        width: 35px;
        height: 35px;
    }

    .bracket-round {
        min-width: 180px;
    }

    .tournament-tab {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .stats-header,
    .stats-row {
        grid-template-columns: 2fr 1fr 1fr;
        padding: 0.75rem;
    }

    .tournament-info-grid {
        grid-template-columns: 1fr;
    }

    .team-social {
        flex-wrap: wrap;
    }
}
