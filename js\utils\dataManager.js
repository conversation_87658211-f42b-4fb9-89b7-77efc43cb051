/**
 * DATA MANAGER UTILITIES
 * 
 * This file contains utility functions for managing and manipulating
 * tournament, team, and match data across the application.
 */

/**
 * Get enriched match data with team information
 * @param {object} match - Match object
 * @returns {object} Match with team details
 */
function enrichMatchData(match) {
    const team1Data = getTeamById(match.team1);
    const team2Data = getTeamById(match.team2);
    const tournamentData = getTournamentById(match.tournamentId);
    
    return {
        ...match,
        team1Data: team1Data || { id: match.team1, name: match.team1, shortName: match.team1 },
        team2Data: team2Data || { id: match.team2, name: match.team2, shortName: match.team2 },
        tournamentData: tournamentData || { id: match.tournamentId, name: match.tournamentId }
    };
}

/**
 * Get enriched tournament data with team information
 * @param {object} tournament - Tournament object
 * @returns {object} Tournament with team details
 */
function enrichTournamentData(tournament) {
    const teamsData = tournament.teams.map(teamId => {
        const team = getTeamById(teamId);
        return team || { id: teamId, name: teamId, shortName: teamId };
    });
    
    return {
        ...tournament,
        teamsData
    };
}

/**
 * Filter matches by multiple criteria
 * @param {object} filters - Filter criteria
 * @returns {array} Filtered matches
 */
function filterMatches(filters = {}) {
    let matches = [...MATCHES];
    
    // Filter by status
    if (filters.status && filters.status !== 'all') {
        matches = matches.filter(match => match.status === filters.status);
    }
    
    // Filter by tournament
    if (filters.tournament) {
        matches = matches.filter(match => match.tournamentId === filters.tournament);
    }
    
    // Filter by team
    if (filters.team) {
        matches = matches.filter(match => 
            match.team1 === filters.team || match.team2 === filters.team
        );
    }
    
    // Filter by date range
    if (filters.startDate && filters.endDate) {
        const start = new Date(filters.startDate);
        const end = new Date(filters.endDate);
        matches = matches.filter(match => {
            const matchDate = new Date(match.scheduledTime);
            return matchDate >= start && matchDate <= end;
        });
    }
    
    // Filter by today
    if (filters.today) {
        matches = matches.filter(match => isToday(match.scheduledTime));
    }
    
    // Sort by scheduled time
    matches.sort((a, b) => {
        if (filters.sortOrder === 'desc') {
            return new Date(b.scheduledTime) - new Date(a.scheduledTime);
        }
        return new Date(a.scheduledTime) - new Date(b.scheduledTime);
    });
    
    // Limit results
    if (filters.limit) {
        matches = matches.slice(0, filters.limit);
    }
    
    return matches.map(enrichMatchData);
}

/**
 * Get team statistics
 * @param {string} teamId - Team ID
 * @returns {object} Team statistics
 */
function getTeamStats(teamId) {
    const teamMatches = getMatchesByTeam(teamId);
    const finishedMatches = teamMatches.filter(match => match.status === 'finished');
    
    let wins = 0;
    let losses = 0;
    let mapsWon = 0;
    let mapsLost = 0;
    
    finishedMatches.forEach(match => {
        if (match.result) {
            if (match.result.winner === teamId) {
                wins++;
                if (match.team1 === teamId) {
                    mapsWon += match.result.score.team1;
                    mapsLost += match.result.score.team2;
                } else {
                    mapsWon += match.result.score.team2;
                    mapsLost += match.result.score.team1;
                }
            } else {
                losses++;
                if (match.team1 === teamId) {
                    mapsWon += match.result.score.team1;
                    mapsLost += match.result.score.team2;
                } else {
                    mapsWon += match.result.score.team2;
                    mapsLost += match.result.score.team1;
                }
            }
        }
    });
    
    const totalMatches = wins + losses;
    const winRate = totalMatches > 0 ? (wins / totalMatches * 100).toFixed(1) : 0;
    const mapWinRate = (mapsWon + mapsLost) > 0 ? (mapsWon / (mapsWon + mapsLost) * 100).toFixed(1) : 0;
    
    return {
        matchesPlayed: totalMatches,
        wins,
        losses,
        winRate: parseFloat(winRate),
        mapsWon,
        mapsLost,
        mapWinRate: parseFloat(mapWinRate)
    };
}

/**
 * Get tournament standings
 * @param {string} tournamentId - Tournament ID
 * @returns {array} Team standings
 */
function getTournamentStandings(tournamentId) {
    const tournament = getTournamentById(tournamentId);
    if (!tournament) return [];
    
    const standings = tournament.teams.map(teamId => {
        const team = getTeamById(teamId);
        const stats = getTeamStats(teamId);
        
        return {
            team: team || { id: teamId, name: teamId },
            ...stats
        };
    });
    
    // Sort by wins, then by map win rate
    standings.sort((a, b) => {
        if (b.wins !== a.wins) {
            return b.wins - a.wins;
        }
        return b.mapWinRate - a.mapWinRate;
    });
    
    return standings;
}

/**
 * Get head-to-head record between two teams
 * @param {string} team1Id - First team ID
 * @param {string} team2Id - Second team ID
 * @returns {object} Head-to-head statistics
 */
function getHeadToHead(team1Id, team2Id) {
    const matches = MATCHES.filter(match => 
        (match.team1 === team1Id && match.team2 === team2Id) ||
        (match.team1 === team2Id && match.team2 === team1Id)
    ).filter(match => match.status === 'finished');
    
    let team1Wins = 0;
    let team2Wins = 0;
    let team1Maps = 0;
    let team2Maps = 0;
    
    matches.forEach(match => {
        if (match.result) {
            if (match.result.winner === team1Id) {
                team1Wins++;
            } else if (match.result.winner === team2Id) {
                team2Wins++;
            }
            
            // Count maps
            if (match.team1 === team1Id) {
                team1Maps += match.result.score.team1;
                team2Maps += match.result.score.team2;
            } else {
                team1Maps += match.result.score.team2;
                team2Maps += match.result.score.team1;
            }
        }
    });
    
    return {
        matchesPlayed: matches.length,
        team1: {
            wins: team1Wins,
            maps: team1Maps
        },
        team2: {
            wins: team2Wins,
            maps: team2Maps
        },
        matches: matches.map(enrichMatchData)
    };
}

/**
 * Get upcoming matches for display
 * @param {number} limit - Number of matches to return
 * @returns {array} Upcoming matches with enriched data
 */
function getUpcomingMatchesForDisplay(limit = 10) {
    return filterMatches({
        status: 'upcoming',
        limit,
        sortOrder: 'asc'
    });
}

/**
 * Get live matches for display
 * @returns {array} Live matches with enriched data
 */
function getLiveMatchesForDisplay() {
    return filterMatches({
        status: 'live',
        sortOrder: 'asc'
    });
}

/**
 * Get recent results for display
 * @param {number} limit - Number of results to return
 * @returns {array} Recent results with enriched data
 */
function getRecentResultsForDisplay(limit = 5) {
    return filterMatches({
        status: 'finished',
        limit,
        sortOrder: 'desc'
    });
}

/**
 * Get today's matches for display
 * @returns {array} Today's matches with enriched data
 */
function getTodayMatchesForDisplay() {
    return filterMatches({
        today: true,
        sortOrder: 'asc'
    });
}

/**
 * Search across all data
 * @param {string} query - Search query
 * @returns {object} Search results
 */
function searchAll(query) {
    const lowercaseQuery = query.toLowerCase();
    
    // Search teams
    const teams = searchTeams(query);
    
    // Search tournaments
    const tournaments = TOURNAMENTS.filter(tournament =>
        tournament.name.toLowerCase().includes(lowercaseQuery) ||
        tournament.shortName.toLowerCase().includes(lowercaseQuery)
    );
    
    // Search matches (by team names)
    const matches = MATCHES.filter(match => {
        const team1 = getTeamById(match.team1);
        const team2 = getTeamById(match.team2);
        const tournament = getTournamentById(match.tournamentId);
        
        return (team1 && team1.name.toLowerCase().includes(lowercaseQuery)) ||
               (team2 && team2.name.toLowerCase().includes(lowercaseQuery)) ||
               (tournament && tournament.name.toLowerCase().includes(lowercaseQuery));
    }).map(enrichMatchData);
    
    return {
        teams,
        tournaments,
        matches,
        total: teams.length + tournaments.length + matches.length
    };
}

/**
 * Get dashboard data
 * @returns {object} Dashboard data summary
 */
function getDashboardData() {
    const upcomingMatches = getUpcomingMatchesForDisplay(5);
    const liveMatches = getLiveMatchesForDisplay();
    const recentResults = getRecentResultsForDisplay(3);
    const todayMatches = getTodayMatchesForDisplay();
    const ongoingTournaments = getOngoingTournaments();
    
    return {
        upcomingMatches,
        liveMatches,
        recentResults,
        todayMatches,
        ongoingTournaments: ongoingTournaments.map(enrichTournamentData),
        stats: {
            totalTeams: TEAMS.length,
            totalTournaments: TOURNAMENTS.length,
            totalMatches: MATCHES.length,
            liveMatchesCount: liveMatches.length,
            upcomingMatchesCount: upcomingMatches.length
        }
    };
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        enrichMatchData,
        enrichTournamentData,
        filterMatches,
        getTeamStats,
        getTournamentStandings,
        getHeadToHead,
        getUpcomingMatchesForDisplay,
        getLiveMatchesForDisplay,
        getRecentResultsForDisplay,
        getTodayMatchesForDisplay,
        searchAll,
        getDashboardData
    };
}
