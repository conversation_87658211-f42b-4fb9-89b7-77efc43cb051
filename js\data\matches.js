/**
 * MATCHES DATA CONFIGURATION
 * 
 * This file contains all match information including schedules, results, and live matches.
 * Matches are linked to tournaments and teams defined in other data files.
 * 
 * HOW TO ADD A NEW MATCH:
 * 1. Add a new object to the MATCHES array
 * 2. Give it a unique ID (use tournament-id + match identifier)
 * 3. Reference existing tournament and team IDs
 * 4. Set appropriate status and schedule
 * 
 * MATCH OBJECT STRUCTURE:
 * {
 *   id: "match-id",                        // Unique identifier
 *   tournamentId: "tournament-id",         // Reference to tournament
 *   team1: "team-id-1",                   // First team ID
 *   team2: "team-id-2",                   // Second team ID
 *   scheduledTime: "2024-01-15T18:00:00Z", // ISO 8601 format
 *   status: "upcoming",                    // upcoming, live, finished, cancelled, postponed
 *   format: "BO3",                        // BO1, BO3, BO5
 *   maps: ["Bind", "Haven", "Split"],     // Map pool for this match
 *   result: {                             // Only present for finished matches
 *     winner: "team-id",                  // Winning team ID
 *     score: { team1: 2, team2: 1 },     // Map score
 *     mapResults: [                       // Individual map results
 *       { map: "Bind", team1Score: 13, team2Score: 11, winner: "team-id-1" },
 *       { map: "Haven", team1Score: 9, team2Score: 13, winner: "team-id-2" },
 *       { map: "Split", team1Score: 13, team2Score: 8, winner: "team-id-1" }
 *     ]
 *   },
 *   stream: {                             // Optional streaming information
 *     platform: "Twitch",                // Twitch, YouTube, etc.
 *     url: "https://twitch.tv/...",      // Stream URL
 *     language: "EN"                     // Stream language
 *   }
 * }
 */

const MATCHES = [
    // VCT Champions 2024 - Quarterfinals
    {
        id: "vct-champions-2024-qf1",
        tournamentId: "vct-champions-2024",
        team1: "sentinels",
        team2: "fnatic",
        scheduledTime: "2024-01-20T20:00:00Z",
        status: "finished",
        format: "BO3",
        maps: ["Bind", "Haven", "Split"],
        result: {
            winner: "sentinels",
            score: { team1: 2, team2: 1 },
            mapResults: [
                { map: "Bind", team1Score: 13, team2Score: 11, winner: "sentinels" },
                { map: "Haven", team1Score: 9, team2Score: 13, winner: "fnatic" },
                { map: "Split", team1Score: 13, team2Score: 8, winner: "sentinels" }
            ]
        },
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    {
        id: "vct-champions-2024-qf2",
        tournamentId: "vct-champions-2024",
        team1: "loud",
        team2: "paper-rex",
        scheduledTime: "2024-01-20T23:00:00Z",
        status: "finished",
        format: "BO3",
        maps: ["Ascent", "Icebox", "Breeze"],
        result: {
            winner: "loud",
            score: { team1: 2, team2: 0 },
            mapResults: [
                { map: "Ascent", team1Score: 13, team2Score: 7, winner: "loud" },
                { map: "Icebox", team1Score: 13, team2Score: 10, winner: "loud" }
            ]
        },
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    {
        id: "vct-champions-2024-qf3",
        tournamentId: "vct-champions-2024",
        team1: "navi",
        team2: "eg",
        scheduledTime: "2024-01-21T18:00:00Z",
        status: "live",
        format: "BO3",
        maps: ["Haven", "Split", "Bind"],
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    {
        id: "vct-champions-2024-qf4",
        tournamentId: "vct-champions-2024",
        team1: "drx",
        team2: "nrg",
        scheduledTime: "2024-01-21T21:00:00Z",
        status: "upcoming",
        format: "BO3",
        maps: ["Fracture", "Ascent", "Icebox"],
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    
    // VCT Champions 2024 - Semifinals
    {
        id: "vct-champions-2024-sf1",
        tournamentId: "vct-champions-2024",
        team1: "sentinels",
        team2: "loud",
        scheduledTime: "2024-01-25T20:00:00Z",
        status: "upcoming",
        format: "BO3",
        maps: ["Bind", "Haven", "Split"],
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    {
        id: "vct-champions-2024-sf2",
        tournamentId: "vct-champions-2024",
        team1: "navi",
        team2: "drx",
        scheduledTime: "2024-01-25T23:00:00Z",
        status: "upcoming",
        format: "BO3",
        maps: ["Ascent", "Icebox", "Breeze"],
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    
    // VCT Champions 2024 - Grand Final
    {
        id: "vct-champions-2024-gf",
        tournamentId: "vct-champions-2024",
        team1: "TBD",
        team2: "TBD",
        scheduledTime: "2024-01-28T20:00:00Z",
        status: "upcoming",
        format: "BO5",
        maps: ["Bind", "Haven", "Split", "Ascent", "Icebox"],
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    
    // Masters Madrid 2024 - Completed matches
    {
        id: "masters-madrid-2024-gf",
        tournamentId: "masters-madrid-2024",
        team1: "sentinels",
        team2: "fnatic",
        scheduledTime: "2024-03-24T19:00:00Z",
        status: "finished",
        format: "BO5",
        maps: ["Bind", "Haven", "Split", "Ascent", "Icebox"],
        result: {
            winner: "sentinels",
            score: { team1: 3, team2: 1 },
            mapResults: [
                { map: "Bind", team1Score: 13, team2Score: 8, winner: "sentinels" },
                { map: "Haven", team1Score: 11, team2Score: 13, winner: "fnatic" },
                { map: "Split", team1Score: 13, team2Score: 6, winner: "sentinels" },
                { map: "Ascent", team1Score: 13, team2Score: 9, winner: "sentinels" }
            ]
        },
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant",
            language: "EN"
        }
    },
    
    // Upcoming Regional Qualifier matches
    {
        id: "na-qualifier-r1-1",
        tournamentId: "regional-qualifier-na",
        team1: "sentinels",
        team2: "eg",
        scheduledTime: "2024-02-01T19:00:00Z",
        status: "upcoming",
        format: "BO1",
        maps: ["Bind"],
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant_na",
            language: "EN"
        }
    },
    {
        id: "na-qualifier-r1-2",
        tournamentId: "regional-qualifier-na",
        team1: "nrg",
        team2: "sentinels",
        scheduledTime: "2024-02-02T19:00:00Z",
        status: "upcoming",
        format: "BO1",
        maps: ["Haven"],
        stream: {
            platform: "Twitch",
            url: "https://twitch.tv/valorant_na",
            language: "EN"
        }
    }
];

/**
 * MATCH STATUS DEFINITIONS
 */
const MATCH_STATUS = {
    UPCOMING: 'upcoming',
    LIVE: 'live',
    FINISHED: 'finished',
    CANCELLED: 'cancelled',
    POSTPONED: 'postponed'
};

/**
 * UTILITY FUNCTIONS FOR MATCHES
 */

// Get match by ID
function getMatchById(matchId) {
    return MATCHES.find(match => match.id === matchId);
}

// Get matches by tournament
function getMatchesByTournament(tournamentId) {
    return MATCHES.filter(match => match.tournamentId === tournamentId);
}

// Get matches by team
function getMatchesByTeam(teamId) {
    return MATCHES.filter(match => 
        match.team1 === teamId || match.team2 === teamId
    );
}

// Get matches by status
function getMatchesByStatus(status) {
    return MATCHES.filter(match => match.status === status);
}

// Get upcoming matches
function getUpcomingMatches() {
    return getMatchesByStatus(MATCH_STATUS.UPCOMING)
        .sort((a, b) => new Date(a.scheduledTime) - new Date(b.scheduledTime));
}

// Get live matches
function getLiveMatches() {
    return getMatchesByStatus(MATCH_STATUS.LIVE);
}

// Get finished matches
function getFinishedMatches() {
    return getMatchesByStatus(MATCH_STATUS.FINISHED)
        .sort((a, b) => new Date(b.scheduledTime) - new Date(a.scheduledTime));
}

// Get matches for today
function getTodayMatches() {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);
    
    return MATCHES.filter(match => {
        const matchDate = new Date(match.scheduledTime);
        return matchDate >= todayStart && matchDate < todayEnd;
    }).sort((a, b) => new Date(a.scheduledTime) - new Date(b.scheduledTime));
}

// Get matches by date range
function getMatchesByDateRange(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return MATCHES.filter(match => {
        const matchDate = new Date(match.scheduledTime);
        return matchDate >= start && matchDate <= end;
    }).sort((a, b) => new Date(a.scheduledTime) - new Date(b.scheduledTime));
}

// Get recent results (last 10 finished matches)
function getRecentResults(limit = 10) {
    return getFinishedMatches().slice(0, limit);
}

// Check if match involves specific team
function isTeamInMatch(matchId, teamId) {
    const match = getMatchById(matchId);
    return match && (match.team1 === teamId || match.team2 === teamId);
}

// Get match winner
function getMatchWinner(matchId) {
    const match = getMatchById(matchId);
    return match && match.result ? match.result.winner : null;
}

// Get match score
function getMatchScore(matchId) {
    const match = getMatchById(matchId);
    return match && match.result ? match.result.score : null;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MATCHES,
        MATCH_STATUS,
        getMatchById,
        getMatchesByTournament,
        getMatchesByTeam,
        getMatchesByStatus,
        getUpcomingMatches,
        getLiveMatches,
        getFinishedMatches,
        getTodayMatches,
        getMatchesByDateRange,
        getRecentResults,
        isTeamInMatch,
        getMatchWinner,
        getMatchScore
    };
}
