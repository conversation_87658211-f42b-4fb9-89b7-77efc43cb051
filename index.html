<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Valorant Tournament Hub</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/valorant-theme.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>VTH</h1>
                <span>Valorant Tournament Hub</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link active">Home</a></li>
                <li><a href="teams.html" class="nav-link">Teams</a></li>
                <li><a href="schedule.html" class="nav-link">Schedule</a></li>
                <li><a href="bracket.html" class="nav-link">Brackets</a></li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1 class="hero-title">Upcoming Matches</h1>
                <p class="hero-subtitle">Stay updated with the latest Valorant tournament action</p>
            </div>
        </section>

        <!-- Upcoming Matches -->
        <section class="matches-section">
            <div class="container">
                <div class="section-header">
                    <h2>Live & Upcoming</h2>
                    <div class="filter-tabs">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="live">Live</button>
                        <button class="filter-btn" data-filter="upcoming">Upcoming</button>
                        <button class="filter-btn" data-filter="today">Today</button>
                    </div>
                </div>
                <div id="matches-container" class="matches-grid">
                    <!-- Matches will be dynamically loaded here -->
                </div>
            </div>
        </section>

        <!-- Recent Results -->
        <section class="results-section">
            <div class="container">
                <div class="section-header">
                    <h2>Recent Results</h2>
                    <a href="schedule.html" class="view-all-btn">View All Results</a>
                </div>
                <div id="results-container" class="matches-grid">
                    <!-- Recent results will be dynamically loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Valorant Tournament Hub. Built for the community.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data/teams.js"></script>
    <script src="js/data/tournaments.js"></script>
    <script src="js/data/matches.js"></script>
    <script src="js/utils/dateUtils.js"></script>
    <script src="js/utils/dataManager.js"></script>
    <script src="js/components/matchCard.js"></script>
    <script src="js/pages/home.js"></script>
</body>
</html>
