/**
 * HOME PAGE FUNCTIONALITY
 * 
 * This file contains all the JavaScript functionality for the home page,
 * including loading matches, filtering, and real-time updates.
 */

class HomePage {
    constructor() {
        this.currentFilter = 'all';
        this.matchesContainer = null;
        this.resultsContainer = null;
        this.filterButtons = null;
        this.updateInterval = null;
        
        this.init();
    }
    
    /**
     * Initialize the home page
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    /**
     * Set up the home page elements and event listeners
     */
    setup() {
        // Get DOM elements
        this.matchesContainer = document.getElementById('matches-container');
        this.resultsContainer = document.getElementById('results-container');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        
        if (!this.matchesContainer || !this.resultsContainer) {
            console.error('Required containers not found');
            return;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Load initial data
        this.loadMatches();
        this.loadRecentResults();
        
        // Set up auto-refresh for live matches
        this.setupAutoRefresh();
        
        // Set up navigation
        this.setupNavigation();
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Filter button event listeners
        this.filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const filter = e.target.getAttribute('data-filter');
                this.setFilter(filter);
            });
        });
        
        // Mobile navigation toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }
        
        // Close mobile menu when clicking on links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
    }
    
    /**
     * Set up navigation highlighting
     */
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage || (currentPage === '' && href === 'index.html')) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
    
    /**
     * Set up auto-refresh for live content
     */
    setupAutoRefresh() {
        // Refresh every 30 seconds
        this.updateInterval = setInterval(() => {
            this.refreshLiveContent();
        }, 30000);
        
        // Clear interval when page is hidden
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                }
            } else {
                if (!this.updateInterval) {
                    this.setupAutoRefresh();
                }
            }
        });
    }
    
    /**
     * Refresh live content (matches with live status)
     */
    refreshLiveContent() {
        const liveMatches = getLiveMatchesForDisplay();
        if (liveMatches.length > 0) {
            this.loadMatches();
        }
    }
    
    /**
     * Set the current filter and update display
     * @param {string} filter - Filter type
     */
    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update filter button states
        this.filterButtons.forEach(button => {
            if (button.getAttribute('data-filter') === filter) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        
        // Reload matches with new filter
        this.loadMatches();
    }
    
    /**
     * Load and display matches based on current filter
     */
    loadMatches() {
        let matches = [];
        
        switch (this.currentFilter) {
            case 'live':
                matches = getLiveMatchesForDisplay();
                break;
            case 'upcoming':
                matches = getUpcomingMatchesForDisplay(20);
                break;
            case 'today':
                matches = getTodayMatchesForDisplay();
                break;
            case 'all':
            default:
                // Get live matches first, then upcoming
                const liveMatches = getLiveMatchesForDisplay();
                const upcomingMatches = getUpcomingMatchesForDisplay(15);
                matches = [...liveMatches, ...upcomingMatches];
                break;
        }
        
        this.renderMatches(matches);
    }
    
    /**
     * Load and display recent results
     */
    loadRecentResults() {
        const recentResults = getRecentResultsForDisplay(6);
        this.renderResults(recentResults);
    }
    
    /**
     * Render matches to the matches container
     * @param {array} matches - Array of match data
     */
    renderMatches(matches) {
        if (!this.matchesContainer) return;
        
        // Show loading state
        this.matchesContainer.innerHTML = '<div class="loading">Loading matches...</div>';
        
        // Simulate slight delay for better UX
        setTimeout(() => {
            renderMatchCards(this.matchesContainer, matches);
            this.addMatchCardAnimations();
        }, 100);
    }
    
    /**
     * Render results to the results container
     * @param {array} results - Array of match results
     */
    renderResults(results) {
        if (!this.resultsContainer) return;
        
        renderMatchCards(this.resultsContainer, results);
        this.addMatchCardAnimations();
    }
    
    /**
     * Add animations to match cards
     */
    addMatchCardAnimations() {
        const cards = document.querySelectorAll('.match-card');
        
        cards.forEach((card, index) => {
            // Add staggered animation delay
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    }
    
    /**
     * Handle search functionality (if search is added later)
     * @param {string} query - Search query
     */
    handleSearch(query) {
        if (!query.trim()) {
            this.loadMatches();
            return;
        }
        
        const searchResults = searchAll(query);
        const matches = searchResults.matches;
        
        this.renderMatches(matches);
    }
    
    /**
     * Get dashboard statistics for display
     * @returns {object} Dashboard stats
     */
    getDashboardStats() {
        const dashboardData = getDashboardData();
        return dashboardData.stats;
    }
    
    /**
     * Update page title with live match count
     */
    updatePageTitle() {
        const liveMatches = getLiveMatchesForDisplay();
        const baseTitle = 'Valorant Tournament Hub';
        
        if (liveMatches.length > 0) {
            document.title = `(${liveMatches.length}) ${baseTitle} - Live Matches`;
        } else {
            document.title = baseTitle;
        }
    }
    
    /**
     * Cleanup when leaving the page
     */
    cleanup() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize the home page when script loads
let homePage;

// Wait for all dependencies to load
function initHomePage() {
    // Check if all required functions are available
    if (typeof getLiveMatchesForDisplay === 'function' && 
        typeof getUpcomingMatchesForDisplay === 'function' &&
        typeof renderMatchCards === 'function') {
        homePage = new HomePage();
    } else {
        // Retry after a short delay
        setTimeout(initHomePage, 100);
    }
}

// Start initialization
initHomePage();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (homePage) {
        homePage.cleanup();
    }
});

// Export for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { HomePage };
}
