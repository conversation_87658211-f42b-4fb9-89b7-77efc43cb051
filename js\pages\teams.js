/**
 * TEAMS PAGE FUNCTIONALITY
 * 
 * This file contains all the JavaScript functionality for the teams page,
 * including loading teams, filtering by region, and search functionality.
 */

class TeamsPage {
    constructor() {
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.teamsContainer = null;
        this.statsContainer = null;
        this.filterButtons = null;
        this.searchInput = null;
        
        this.init();
    }
    
    /**
     * Initialize the teams page
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    /**
     * Set up the teams page elements and event listeners
     */
    setup() {
        // Get DOM elements
        this.teamsContainer = document.getElementById('teams-container');
        this.statsContainer = document.getElementById('team-stats-container');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.searchInput = document.getElementById('team-search');
        
        if (!this.teamsContainer) {
            console.error('Teams container not found');
            return;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Load initial data
        this.loadTeams();
        this.loadTeamStats();
        
        // Set up navigation
        this.setupNavigation();
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Filter button event listeners
        this.filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const filter = e.target.getAttribute('data-filter');
                this.setFilter(filter);
            });
        });
        
        // Search input event listener
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.setSearchQuery(e.target.value);
            });
            
            // Clear search on escape key
            this.searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.clearSearch();
                }
            });
        }
        
        // Mobile navigation toggle
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }
        
        // Close mobile menu when clicking on links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (navMenu) navMenu.classList.remove('active');
                if (navToggle) navToggle.classList.remove('active');
            });
        });
    }
    
    /**
     * Set up navigation highlighting
     */
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
    
    /**
     * Set the current filter and update display
     * @param {string} filter - Filter type (region or 'all')
     */
    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update filter button states
        this.filterButtons.forEach(button => {
            if (button.getAttribute('data-filter') === filter) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        
        // Reload teams with new filter
        this.loadTeams();
    }
    
    /**
     * Set search query and update display
     * @param {string} query - Search query
     */
    setSearchQuery(query) {
        this.searchQuery = query.trim();
        this.loadTeams();
    }
    
    /**
     * Clear search input and results
     */
    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
        }
        this.searchQuery = '';
        this.loadTeams();
    }
    
    /**
     * Load and display teams based on current filter and search
     */
    loadTeams() {
        let teams = [...TEAMS];
        
        // Apply region filter
        if (this.currentFilter !== 'all') {
            teams = teams.filter(team => team.region === this.currentFilter);
        }
        
        // Apply search filter
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            teams = teams.filter(team => 
                team.name.toLowerCase().includes(query) ||
                team.shortName.toLowerCase().includes(query) ||
                team.region.toLowerCase().includes(query) ||
                team.players.some(player => 
                    player.name.toLowerCase().includes(query) ||
                    player.role.toLowerCase().includes(query)
                )
            );
        }
        
        // Sort teams alphabetically
        teams.sort((a, b) => a.name.localeCompare(b.name));
        
        this.renderTeams(teams);
    }
    
    /**
     * Load and display team statistics
     */
    loadTeamStats() {
        if (!this.statsContainer) return;
        
        // Get teams with match data
        const teamsWithMatches = TEAMS.filter(team => {
            const stats = getTeamStats(team.id);
            return stats.matchesPlayed > 0;
        });
        
        // Sort by win rate, then by matches played
        teamsWithMatches.sort((a, b) => {
            const statsA = getTeamStats(a.id);
            const statsB = getTeamStats(b.id);
            
            if (statsB.winRate !== statsA.winRate) {
                return statsB.winRate - statsA.winRate;
            }
            return statsB.matchesPlayed - statsA.matchesPlayed;
        });
        
        this.renderTeamStats(teamsWithMatches);
    }
    
    /**
     * Render teams to the teams container
     * @param {array} teams - Array of team data
     */
    renderTeams(teams) {
        if (!this.teamsContainer) return;
        
        // Show loading state
        this.teamsContainer.innerHTML = '<div class="loading">Loading teams...</div>';
        
        // Simulate slight delay for better UX
        setTimeout(() => {
            renderTeamCards(this.teamsContainer, teams, false);
            this.addTeamCardAnimations();
        }, 100);
    }
    
    /**
     * Render team statistics
     * @param {array} teams - Array of team data
     */
    renderTeamStats(teams) {
        if (!this.statsContainer) return;
        
        if (teams.length === 0) {
            this.statsContainer.innerHTML = '<p>No team statistics available yet.</p>';
            return;
        }
        
        const statsTable = createTeamStatsTable(teams);
        this.statsContainer.innerHTML = '';
        this.statsContainer.appendChild(statsTable);
    }
    
    /**
     * Add animations to team cards
     */
    addTeamCardAnimations() {
        const cards = document.querySelectorAll('.team-card');
        
        cards.forEach((card, index) => {
            // Add staggered animation delay
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    }
    
    /**
     * Get region statistics
     * @returns {object} Region statistics
     */
    getRegionStats() {
        const regions = {};
        
        TEAMS.forEach(team => {
            if (!regions[team.region]) {
                regions[team.region] = {
                    count: 0,
                    teams: []
                };
            }
            regions[team.region].count++;
            regions[team.region].teams.push(team);
        });
        
        return regions;
    }
    
    /**
     * Show team details modal (placeholder)
     * @param {string} teamId - Team ID
     */
    showTeamDetails(teamId) {
        const team = getTeamById(teamId);
        if (!team) return;
        
        // This would show a detailed modal with team information
        console.log('Show team details for:', team);
        
        // Example implementation:
        // const modal = new TeamDetailsModal(team);
        // modal.show();
    }
    
    /**
     * Export teams data (for potential future use)
     * @returns {string} JSON string of teams data
     */
    exportTeamsData() {
        const teamsWithStats = TEAMS.map(team => ({
            ...team,
            stats: getTeamStats(team.id)
        }));
        
        return JSON.stringify(teamsWithStats, null, 2);
    }
}

// Initialize the teams page when script loads
let teamsPage;

// Wait for all dependencies to load
function initTeamsPage() {
    // Check if all required functions are available
    if (typeof TEAMS !== 'undefined' && 
        typeof getTeamStats === 'function' &&
        typeof renderTeamCards === 'function' &&
        typeof createTeamStatsTable === 'function') {
        teamsPage = new TeamsPage();
    } else {
        // Retry after a short delay
        setTimeout(initTeamsPage, 100);
    }
}

// Start initialization
initTeamsPage();

// Export for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TeamsPage };
}
