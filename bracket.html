<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brackets - Valorant Tournament Hub</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/valorant-theme.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>VTH</h1>
                <span>Valorant Tournament Hub</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="teams.html" class="nav-link">Teams</a></li>
                <li><a href="schedule.html" class="nav-link">Schedule</a></li>
                <li><a href="bracket.html" class="nav-link active">Brackets</a></li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1 class="hero-title">Tournament Brackets</h1>
                <p class="hero-subtitle">Follow the tournament progression and results</p>
            </div>
        </section>

        <!-- Tournament Selector -->
        <section class="tournament-selector-section">
            <div class="container">
                <div class="tournament-selector">
                    <h3>Select Tournament</h3>
                    <div id="tournament-tabs" class="tournament-tabs">
                        <!-- Tournament tabs will be dynamically loaded here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Bracket Section -->
        <section class="bracket-section">
            <div class="container">
                <div class="section-header">
                    <h2 id="bracket-title">Tournament Bracket</h2>
                    <div class="bracket-info">
                        <span id="tournament-format" class="tournament-format"></span>
                        <span id="tournament-status" class="tournament-status"></span>
                    </div>
                </div>

                <!-- Bracket Container -->
                <div id="bracket-container" class="bracket-container">
                    <!-- Bracket will be dynamically loaded here -->
                </div>

                <!-- Tournament Info -->
                <div id="tournament-info" class="tournament-info-panel">
                    <!-- Tournament details will be loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Valorant Tournament Hub. Built for the community.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data/teams.js"></script>
    <script src="js/data/tournaments.js"></script>
    <script src="js/data/matches.js"></script>
    <script src="js/utils/dateUtils.js"></script>
    <script src="js/utils/dataManager.js"></script>
    <script src="js/components/bracket.js"></script>
    <script src="js/pages/bracket.js"></script>
</body>
</html>
