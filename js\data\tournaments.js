/**
 * TOURNAMENTS DATA CONFIGURATION
 * 
 * This file contains all tournament information and structure.
 * Each tournament defines its format, participating teams, and bracket structure.
 * 
 * HOW TO ADD A NEW TOURNAMENT:
 * 1. Add a new object to the TOURNAMENTS array
 * 2. Give it a unique ID (use lowercase, no spaces, hyphens ok)
 * 3. Define the tournament format and participating teams
 * 4. The bracket structure will be auto-generated based on format
 * 
 * TOURNAMENT OBJECT STRUCTURE:
 * {
 *   id: "tournament-id",                    // Unique identifier
 *   name: "Tournament Name",                // Display name
 *   shortName: "TN",                       // Abbreviation
 *   format: "single-elimination",          // single-elimination, double-elimination, round-robin, swiss
 *   status: "upcoming",                    // upcoming, ongoing, completed, cancelled
 *   startDate: "2024-01-15",              // YYYY-MM-DD format
 *   endDate: "2024-01-20",                // YYYY-MM-DD format
 *   prizePool: "$100,000",                // Prize pool string
 *   location: "Los Angeles, CA",          // Tournament location
 *   teams: ["team-id-1", "team-id-2"],   // Array of team IDs from teams.js
 *   description: "Tournament description", // Brief description
 *   rules: {                              // Tournament-specific rules
 *     mapPool: ["Bind", "Haven", "Split"], // Available maps
 *     matchFormat: "BO3",                 // BO1, BO3, BO5
 *     overtime: true                      // Enable overtime
 *   }
 * }
 */

const TOURNAMENTS = [
    {
        id: "vct-champions-2024",
        name: "VCT Champions 2024",
        shortName: "Champions",
        format: "single-elimination",
        status: "ongoing",
        startDate: "2024-01-15",
        endDate: "2024-01-28",
        prizePool: "$1,000,000",
        location: "Los Angeles, CA",
        teams: ["sentinels", "fnatic", "loud", "paper-rex", "navi", "eg", "drx", "nrg"],
        description: "The ultimate Valorant championship featuring the world's best teams competing for the title.",
        rules: {
            mapPool: ["Bind", "Haven", "Split", "Ascent", "Icebox", "Breeze", "Fracture"],
            matchFormat: "BO3",
            overtime: true,
            timeoutCount: 2,
            roundLimit: 24
        },
        bracket: {
            rounds: [
                {
                    name: "Quarterfinals",
                    matches: [
                        { id: "qf1", team1: "sentinels", team2: "fnatic" },
                        { id: "qf2", team1: "loud", team2: "paper-rex" },
                        { id: "qf3", team1: "navi", team2: "eg" },
                        { id: "qf4", team1: "drx", team2: "nrg" }
                    ]
                },
                {
                    name: "Semifinals",
                    matches: [
                        { id: "sf1", team1: "winner-qf1", team2: "winner-qf2" },
                        { id: "sf2", team1: "winner-qf3", team2: "winner-qf4" }
                    ]
                },
                {
                    name: "Grand Final",
                    matches: [
                        { id: "gf", team1: "winner-sf1", team2: "winner-sf2" }
                    ]
                }
            ]
        }
    },
    {
        id: "masters-madrid-2024",
        name: "VCT Masters Madrid 2024",
        shortName: "Madrid",
        format: "double-elimination",
        status: "completed",
        startDate: "2024-03-14",
        endDate: "2024-03-24",
        prizePool: "$500,000",
        location: "Madrid, Spain",
        teams: ["sentinels", "fnatic", "loud", "paper-rex"],
        description: "International Masters event featuring top teams from each region.",
        rules: {
            mapPool: ["Bind", "Haven", "Split", "Ascent", "Icebox", "Breeze"],
            matchFormat: "BO3",
            overtime: true,
            timeoutCount: 2,
            roundLimit: 24
        },
        bracket: {
            rounds: [
                {
                    name: "Upper Bracket Semifinals",
                    matches: [
                        { id: "ubs1", team1: "sentinels", team2: "fnatic" },
                        { id: "ubs2", team1: "loud", team2: "paper-rex" }
                    ]
                },
                {
                    name: "Lower Bracket Round 1",
                    matches: [
                        { id: "lbr1", team1: "loser-ubs1", team2: "loser-ubs2" }
                    ]
                },
                {
                    name: "Upper Bracket Final",
                    matches: [
                        { id: "ubf", team1: "winner-ubs1", team2: "winner-ubs2" }
                    ]
                },
                {
                    name: "Lower Bracket Final",
                    matches: [
                        { id: "lbf", team1: "winner-lbr1", team2: "loser-ubf" }
                    ]
                },
                {
                    name: "Grand Final",
                    matches: [
                        { id: "gf", team1: "winner-ubf", team2: "winner-lbf" }
                    ]
                }
            ]
        }
    },
    {
        id: "regional-qualifier-na",
        name: "North America Regional Qualifier",
        shortName: "NA Qualifier",
        format: "swiss",
        status: "upcoming",
        startDate: "2024-02-01",
        endDate: "2024-02-10",
        prizePool: "$50,000",
        location: "Online",
        teams: ["sentinels", "eg", "nrg"],
        description: "Regional qualifier to determine North America's representatives for international events.",
        rules: {
            mapPool: ["Bind", "Haven", "Split", "Ascent", "Icebox"],
            matchFormat: "BO1",
            overtime: true,
            timeoutCount: 1,
            roundLimit: 24,
            swissRounds: 5
        }
    },
    {
        id: "showmatch-invitational",
        name: "Community Showmatch Invitational",
        shortName: "Showmatch",
        format: "round-robin",
        status: "upcoming",
        startDate: "2024-01-25",
        endDate: "2024-01-26",
        prizePool: "$25,000",
        location: "Online",
        teams: ["sentinels", "fnatic", "loud", "paper-rex"],
        description: "Fun invitational tournament featuring fan-favorite teams in a round-robin format.",
        rules: {
            mapPool: ["Bind", "Haven", "Split", "Ascent"],
            matchFormat: "BO1",
            overtime: false,
            timeoutCount: 1,
            roundLimit: 20
        }
    }
];

/**
 * TOURNAMENT STATUS DEFINITIONS
 */
const TOURNAMENT_STATUS = {
    UPCOMING: 'upcoming',
    ONGOING: 'ongoing',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

/**
 * TOURNAMENT FORMAT DEFINITIONS
 */
const TOURNAMENT_FORMATS = {
    SINGLE_ELIMINATION: 'single-elimination',
    DOUBLE_ELIMINATION: 'double-elimination',
    ROUND_ROBIN: 'round-robin',
    SWISS: 'swiss'
};

/**
 * UTILITY FUNCTIONS FOR TOURNAMENTS
 */

// Get tournament by ID
function getTournamentById(tournamentId) {
    return TOURNAMENTS.find(tournament => tournament.id === tournamentId);
}

// Get tournaments by status
function getTournamentsByStatus(status) {
    return TOURNAMENTS.filter(tournament => tournament.status === status);
}

// Get ongoing tournaments
function getOngoingTournaments() {
    return getTournamentsByStatus(TOURNAMENT_STATUS.ONGOING);
}

// Get upcoming tournaments
function getUpcomingTournaments() {
    return getTournamentsByStatus(TOURNAMENT_STATUS.UPCOMING);
}

// Get completed tournaments
function getCompletedTournaments() {
    return getTournamentsByStatus(TOURNAMENT_STATUS.COMPLETED);
}

// Get tournaments by date range
function getTournamentsByDateRange(startDate, endDate) {
    return TOURNAMENTS.filter(tournament => {
        const tournamentStart = new Date(tournament.startDate);
        const tournamentEnd = new Date(tournament.endDate);
        const rangeStart = new Date(startDate);
        const rangeEnd = new Date(endDate);
        
        return (tournamentStart >= rangeStart && tournamentStart <= rangeEnd) ||
               (tournamentEnd >= rangeStart && tournamentEnd <= rangeEnd) ||
               (tournamentStart <= rangeStart && tournamentEnd >= rangeEnd);
    });
}

// Get tournaments featuring a specific team
function getTournamentsByTeam(teamId) {
    return TOURNAMENTS.filter(tournament => 
        tournament.teams.includes(teamId)
    );
}

// Check if tournament is active (ongoing or upcoming)
function isTournamentActive(tournamentId) {
    const tournament = getTournamentById(tournamentId);
    return tournament && (tournament.status === TOURNAMENT_STATUS.ONGOING || tournament.status === TOURNAMENT_STATUS.UPCOMING);
}

// Get tournament duration in days
function getTournamentDuration(tournamentId) {
    const tournament = getTournamentById(tournamentId);
    if (!tournament) return 0;
    
    const start = new Date(tournament.startDate);
    const end = new Date(tournament.endDate);
    const diffTime = Math.abs(end - start);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TOURNAMENTS,
        TOURNAMENT_STATUS,
        TOURNAMENT_FORMATS,
        getTournamentById,
        getTournamentsByStatus,
        getOngoingTournaments,
        getUpcomingTournaments,
        getCompletedTournaments,
        getTournamentsByDateRange,
        getTournamentsByTeam,
        isTournamentActive,
        getTournamentDuration
    };
}
