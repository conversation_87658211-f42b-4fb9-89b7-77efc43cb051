/**
 * DATE UTILITIES
 * 
 * This file contains utility functions for date formatting and manipulation
 * used throughout the tournament management system.
 */

/**
 * Format a date string for display
 * @param {string} dateString - ISO date string
 * @param {string} format - Format type: 'full', 'short', 'time', 'date'
 * @returns {string} Formatted date string
 */
function formatDate(dateString, format = 'full') {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
        return 'Invalid Date';
    }
    
    const options = {
        full: {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
        },
        short: {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        },
        date: {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        },
        time: {
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
        },
        compact: {
            month: 'numeric',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }
    };
    
    return date.toLocaleDateString('en-US', options[format] || options.full);
}

/**
 * Get relative time string (e.g., "2 hours ago", "in 3 days")
 * @param {string} dateString - ISO date string
 * @returns {string} Relative time string
 */
function getRelativeTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (Math.abs(diffMinutes) < 1) {
        return 'Now';
    } else if (Math.abs(diffMinutes) < 60) {
        return diffMinutes > 0 ? `in ${diffMinutes}m` : `${Math.abs(diffMinutes)}m ago`;
    } else if (Math.abs(diffHours) < 24) {
        return diffHours > 0 ? `in ${diffHours}h` : `${Math.abs(diffHours)}h ago`;
    } else if (Math.abs(diffDays) < 7) {
        return diffDays > 0 ? `in ${diffDays}d` : `${Math.abs(diffDays)}d ago`;
    } else {
        return formatDate(dateString, 'date');
    }
}

/**
 * Check if a date is today
 * @param {string} dateString - ISO date string
 * @returns {boolean} True if date is today
 */
function isToday(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
}

/**
 * Check if a date is tomorrow
 * @param {string} dateString - ISO date string
 * @returns {boolean} True if date is tomorrow
 */
function isTomorrow(dateString) {
    const date = new Date(dateString);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return date.getDate() === tomorrow.getDate() &&
           date.getMonth() === tomorrow.getMonth() &&
           date.getFullYear() === tomorrow.getFullYear();
}

/**
 * Check if a date is in the past
 * @param {string} dateString - ISO date string
 * @returns {boolean} True if date is in the past
 */
function isPast(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    return date < now;
}

/**
 * Check if a date is in the future
 * @param {string} dateString - ISO date string
 * @returns {boolean} True if date is in the future
 */
function isFuture(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    return date > now;
}

/**
 * Get time until a specific date
 * @param {string} dateString - ISO date string
 * @returns {object} Object with days, hours, minutes, seconds until date
 */
function getTimeUntil(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    
    if (diffMs <= 0) {
        return { days: 0, hours: 0, minutes: 0, seconds: 0, isPast: true };
    }
    
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
    
    return { days, hours, minutes, seconds, isPast: false };
}

/**
 * Format countdown timer
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted countdown string
 */
function formatCountdown(dateString) {
    const timeUntil = getTimeUntil(dateString);
    
    if (timeUntil.isPast) {
        return 'Started';
    }
    
    if (timeUntil.days > 0) {
        return `${timeUntil.days}d ${timeUntil.hours}h ${timeUntil.minutes}m`;
    } else if (timeUntil.hours > 0) {
        return `${timeUntil.hours}h ${timeUntil.minutes}m`;
    } else if (timeUntil.minutes > 0) {
        return `${timeUntil.minutes}m ${timeUntil.seconds}s`;
    } else {
        return `${timeUntil.seconds}s`;
    }
}

/**
 * Get day of week
 * @param {string} dateString - ISO date string
 * @returns {string} Day of week (e.g., "Monday")
 */
function getDayOfWeek(dateString) {
    const date = new Date(dateString);
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[date.getDay()];
}

/**
 * Get month name
 * @param {string} dateString - ISO date string
 * @returns {string} Month name (e.g., "January")
 */
function getMonthName(dateString) {
    const date = new Date(dateString);
    const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[date.getMonth()];
}

/**
 * Convert UTC time to local timezone
 * @param {string} utcDateString - UTC date string
 * @returns {Date} Local date object
 */
function utcToLocal(utcDateString) {
    return new Date(utcDateString);
}

/**
 * Get timezone abbreviation
 * @returns {string} Timezone abbreviation (e.g., "PST", "EST")
 */
function getTimezone() {
    const date = new Date();
    const timeZone = date.toLocaleDateString('en', {timeZoneName:'short'}).split(', ')[1];
    return timeZone;
}

/**
 * Format match time with smart formatting
 * @param {string} dateString - ISO date string
 * @returns {string} Smart formatted time
 */
function formatMatchTime(dateString) {
    if (isToday(dateString)) {
        return `Today ${formatDate(dateString, 'time')}`;
    } else if (isTomorrow(dateString)) {
        return `Tomorrow ${formatDate(dateString, 'time')}`;
    } else {
        const dayOfWeek = getDayOfWeek(dateString);
        return `${dayOfWeek} ${formatDate(dateString, 'short')}`;
    }
}

/**
 * Get status based on match time
 * @param {string} dateString - ISO date string
 * @param {string} currentStatus - Current match status
 * @returns {string} Time-based status
 */
function getTimeBasedStatus(dateString, currentStatus) {
    if (currentStatus === 'live') {
        return 'LIVE';
    } else if (currentStatus === 'finished') {
        return 'FINISHED';
    } else if (currentStatus === 'cancelled') {
        return 'CANCELLED';
    } else if (currentStatus === 'postponed') {
        return 'POSTPONED';
    }
    
    const now = new Date();
    const matchTime = new Date(dateString);
    const diffMinutes = (matchTime - now) / (1000 * 60);
    
    if (diffMinutes <= 0) {
        return 'STARTING';
    } else if (diffMinutes <= 30) {
        return 'STARTING SOON';
    } else {
        return 'UPCOMING';
    }
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatDate,
        getRelativeTime,
        isToday,
        isTomorrow,
        isPast,
        isFuture,
        getTimeUntil,
        formatCountdown,
        getDayOfWeek,
        getMonthName,
        utcToLocal,
        getTimezone,
        formatMatchTime,
        getTimeBasedStatus
    };
}
