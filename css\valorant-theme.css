/* ===== VALORANT COLOR SCHEME ===== */
:root {
    /* Primary Colors */
    --bg-primary: #0f1419;
    --bg-secondary: #1a1f2e;
    --bg-tertiary: #252b3a;
    
    /* Accent Colors */
    --accent-primary: #ff4655;
    --accent-secondary: #fd4556;
    --accent-tertiary: #ff6b7a;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8bcc8;
    --text-tertiary: #8a8f98;
    
    /* Border & Divider Colors */
    --border-color: #2d3748;
    --border-light: #3a4553;
    
    /* Status Colors */
    --status-live: #ff4655;
    --status-upcoming: #4fd1c7;
    --status-finished: #68d391;
    --status-cancelled: #fc8181;
    
    /* Team Colors */
    --team-win: #68d391;
    --team-loss: #fc8181;
    
    /* Gradient Backgrounds */
    --gradient-primary: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
    --gradient-accent: linear-gradient(135deg, #ff4655 0%, #fd4556 100%);
    --gradient-card: linear-gradient(135deg, #1a1f2e 0%, #252b3a 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    --shadow-accent: 0 4px 12px rgba(255, 70, 85, 0.3);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== VALORANT-SPECIFIC STYLING ===== */

/* Glowing Effects */
.glow-accent {
    box-shadow: 0 0 20px rgba(255, 70, 85, 0.3);
}

.glow-accent:hover {
    box-shadow: 0 0 30px rgba(255, 70, 85, 0.5);
}

/* Animated Borders */
.animated-border {
    position: relative;
    overflow: hidden;
}

.animated-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: var(--gradient-accent);
    animation: borderSlide 2s infinite;
}

@keyframes borderSlide {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-live {
    background: var(--status-live);
    animation: pulse 2s infinite;
}

.status-upcoming {
    background: var(--status-upcoming);
}

.status-finished {
    background: var(--status-finished);
}

.status-cancelled {
    background: var(--status-cancelled);
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.2); }
    100% { opacity: 1; transform: scale(1); }
}

/* Valorant-style Buttons */
.btn-primary {
    background: var(--gradient-accent);
    border: none;
    color: var(--text-primary);
    padding: 12px 24px;
    border-radius: var(--radius-sm);
    font-family: inherit;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-accent);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-secondary {
    background: transparent;
    border: 2px solid var(--accent-primary);
    color: var(--accent-primary);
    padding: 10px 22px;
    border-radius: var(--radius-sm);
    font-family: inherit;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-secondary:hover {
    background: var(--accent-primary);
    color: var(--text-primary);
    transform: translateY(-2px);
}

/* Valorant-style Cards */
.card {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card:hover {
    border-color: var(--accent-primary);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.card:hover::before {
    transform: scaleX(1);
}

/* Typography Enhancements */
.text-accent {
    color: var(--accent-primary);
}

.text-gradient {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-glow {
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

/* Selection Styling */
::selection {
    background: var(--accent-primary);
    color: var(--text-primary);
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.hidden { display: none; }
.visible { display: block; }

.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

.grid { display: grid; }
.grid-center { place-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }
